#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <DNSServer.h>
#include "LittleFS.h"

// 前向声明，避免循环包含
struct PhotoMetadata;
class PhotoGallery;
#include <Preferences.h>

// WiFi管理模块
class WifiManager {
public:
    WifiManager();
    void setPhotoGallery(PhotoGallery* gallery) { photoGallery = gallery; }
    void init();  // 初始化WiFi管理器
    void handleClient();  // 处理客户端请求
    void setNTPAutoSyncCallback(void (*callback)(const String&, const String&));  // 设置NTP自动同步回调
    void setModeChangeCallback(void (*callback)());  // 设置模式切换回调

    // 公有访问方法
    bool* getOfflineModePtr() { return &offlineModeEnabled; }
    bool getStockModeEnabled() { return stockModeEnabled; }
    bool getGalleryModeEnabled() { return galleryModeEnabled; }
    bool isAPModeActive() const;  // 检测WiFi AP是否开启
    String getNTPServer() { return savedNtpServer; }
    String getTimezone() { return savedTimezone; }

    // 初始化状态查询方法
    bool getInitialWifiConnected() { return initialWifiConnected; }
    bool getInitialNtpSynced() { return initialNtpSynced; }
    bool isInitializationComplete() { return initializationComplete; }

    // 分步初始化方法（公有访问）
    void loadDeviceSettings();  // 加载设备设置
    void loadClockSettings();   // 加载时钟设置
    void loadOfflineMode();     // 加载离线模式设置
    void loadStockSettings();   // 加载股票设置
    void loadGallerySettings(); // 加载相册设置
    void setupAP();             // 设置AP模式
    void setupDNS();            // 设置DNS服务器
    void setupWebServer();      // 设置Web服务器
    void performSynchronousInitialization();  // 执行同步初始化（WiFi连接和NTP同步）

    // 公有成员变量（用于外部访问）
    bool offlineModeEnabled;  // 离线模式开关
    bool stockModeEnabled;    // 股票模式开关
    bool galleryModeEnabled;  // 相册模式开关
    String stockCodes[4];     // 股票代码数组
    String savedTimezone;
    String savedNtpServer;

    // 初始化状态记录
    bool initialWifiConnected;    // 记录初始WiFi连接是否成功
    bool initialNtpSynced;        // 记录初始NTP同步是否成功
    bool initializationComplete;  // 记录初始化是否完成

    // 最终确定的系统模式（初始化后不再改变）
    bool finalAutoMode;           // true=自动模式，false=离线模式
    String finalWifiSSID;         // 最终连接的WiFi SSID（如果有）
    String finalWifiIP;           // 最终的WiFi IP地址（如果有）
    bool wifiRetryDisabled;       // 禁用WiFi重试标志（用于离线模式切换）
    
private:
    WebServer server;
    DNSServer dnsServer;
    Preferences preferences;


    void handleRoot();  // 处理根路径请求
    void handleOfflineMode();  // 处理离线模式页面
    void handleSetTime();  // 处理时间设置请求
    void handleOfflineModeToggle();  // 处理离线模式开关设置
    void handleRestart();  // 处理重启设备请求
    void handleRestartPage();  // 处理重启设备页面
    void handleWifiConfig();  // 处理WiFi配置页面
    void handleWifiScan();  // 处理WiFi扫描请求
    void handleWifiConnect();  // 处理WiFi连接请求
    void handleWifiStatus();  // 处理WiFi状态查询（返回固定状态）
    void saveWifiCredentials(String ssid, String password);  // 保存WiFi凭据
    void loadWifiCredentials();  // 加载WiFi凭据
    bool performSynchronousNTPSync();  // 执行同步NTP时间同步
    bool isQuickWiFiFailure(wl_status_t status);  // 检测WiFi快速失败状态
    void resetWiFiForScan();  // 重置WiFi状态以支持扫描功能
    void handleClockSetting();  // 处理时钟设置页面
    void handleTimeSync();  // 处理时间同步请求
    void saveClockSettings(String timezone, String ntpServer);  // 保存时钟设置
    void syncTimeFromNTP();  // 从NTP服务器同步时间
    void saveOfflineMode(bool enabled);  // 保存离线模式设置
    void handleDeviceConfig();  // 处理设备配置页面
    void handleDeviceSettings();  // 处理设备设置请求
    void handleBrightnessTest();  // 处理亮度测试请求
    void saveDeviceSettings(int brightness, String orientation);  // 保存设备设置
    void applyBrightness(int brightness);  // 应用亮度设置
    void handleStockConfig();  // 处理股票配置页面
    void handleStockSettings();  // 处理股票设置请求
    void saveStockSettings(bool enabled, String codes[4]);  // 保存股票设置
    void handleGallery();  // 处理电子相册页面
    void handleGallerySettings();  // 处理相册设置请求
    void handlePhotoUpload();  // 处理照片上传
    void handlePhotoDelete();  // 处理照片删除
    void saveGallerySettings(bool enabled);  // 保存相册设置
    bool isValidStockCode(String code);  // 验证股票代码

    // WiFi连接状态管理（仅用于用户主动连接时的临时状态）
    enum WifiConnectionState {
        WIFI_IDLE,
        WIFI_CONNECTING,
        WIFI_CONNECTED,
        WIFI_FAILED,
        WIFI_WRONG_PASSWORD,
        WIFI_TIMEOUT
    };

    WifiConnectionState currentWifiState;
    unsigned long wifiConnectStartTime;
    String connectingSSID;

    // 相册模块引用
    PhotoGallery* photoGallery;

    // 时钟设置相关（已移到public区域）

    // 设备配置相关
    int savedBrightness;
    String savedOrientation;

    // NTP自动同步回调
    void (*ntpAutoSyncCallback)(const String&, const String&);

    // 模式切换回调
    void (*modeChangeCallback)();

    // 异步WiFi扫描相关
    enum WifiScanState {
        SCAN_IDLE,
        SCAN_IN_PROGRESS,
        SCAN_COMPLETED,
        SCAN_FAILED
    };

    WifiScanState scanState;
    unsigned long scanStartTime;
    String cachedScanResult;  // 缓存扫描结果
    void updateWifiScanState();  // 更新WiFi扫描状态
};

#endif // WIFI_MANAGER_H
