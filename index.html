<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>马里奥时钟 - WiFi配置</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
            min-height: 100vh;
            padding: 20px;
            -webkit-tap-highlight-color: transparent;
            position: relative;
        }

        /* 像素云朵背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px),
                radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px),
                radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px),
                radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px);
            background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px;
            animation: cloudMove 20s linear infinite;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes cloudMove {
            0% { transform: translateX(-50px); }
            100% { transform: translateX(50px); }
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%);
            border: 4px solid #654321;
            border-radius: 0;
            box-shadow: 
                inset 2px 2px 0px #CD853F,
                inset -2px -2px 0px #5D4037,
                4px 4px 0px #3E2723;
            overflow: hidden;
            position: relative;
            z-index: 1;
            animation: blockAppear 0.6s ease-out;
        }

        @keyframes blockAppear {
            0% {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 砖块纹理 */
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(90deg, #654321 1px, transparent 1px),
                linear-gradient(180deg, #654321 1px, transparent 1px);
            background-size: 20px 20px;
            opacity: 0.3;
            pointer-events: none;
        }

        /* 页面标题区域 - 马里奥红色主题 */
        .header {
            background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%);
            color: white;
            text-align: center;
            padding: 25px 20px;
            position: relative;
            border-bottom: 4px solid #B91C1C;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%);
            background-size: 8px 8px;
            background-position: 0 0, 4px 4px;
        }

        .header h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
            text-shadow: 2px 2px 0px #8B0000;
            letter-spacing: 1px;
        }

        .header .subtitle {
            font-size: 12px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            text-shadow: 1px 1px 0px #8B0000;
        }

        /* WiFi状态区域 - 绿色管道主题 */
        .status-section {
            background: linear-gradient(145deg, #38A169 0%, #2F855A 100%);
            padding: 20px;
            border-bottom: 4px solid #1A365D;
            position: relative;
        }

        .status-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                repeating-linear-gradient(90deg, 
                    transparent 0px, 
                    transparent 18px, 
                    rgba(0,0,0,0.1) 18px, 
                    rgba(0,0,0,0.1) 20px);
            pointer-events: none;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-size: 13px;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 0px #1A365D;
        }

        .status-value {
            font-size: 13px;
            color: #F7FAFC;
            font-weight: bold;
            background: rgba(0,0,0,0.2);
            padding: 2px 6px;
            border-radius: 2px;
        }

        .status-connected {
            color: #68D391;
            background: rgba(104, 211, 145, 0.2);
        }

        .status-disconnected {
            color: #FC8181;
            background: rgba(252, 129, 129, 0.2);
        }

        /* 像素风格信号强度 */
        .signal-strength {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
            align-items: flex-end;
        }

        .signal-bar {
            width: 4px;
            background-color: rgba(255,255,255,0.3);
            image-rendering: pixelated;
        }

        .signal-bar.active {
            background-color: #F7FAFC;
            box-shadow: 0 0 2px #68D391;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 8px; }
        .signal-bar:nth-child(3) { height: 12px; }
        .signal-bar:nth-child(4) { height: 16px; }

        /* 功能菜单区域 */
        .menu-section {
            background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%);
            padding: 25px 20px;
            position: relative;
        }

        /* 像素风格按钮 */
        .menu-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
            max-width: 300px;
            margin: 0 auto 15px auto;
            padding: 15px 20px;
            background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%);
            color: white;
            text-decoration: none;
            border: 3px solid #1A365D;
            border-radius: 0;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
            cursor: pointer;
            transition: all 0.1s ease;
            position: relative;
            text-shadow: 1px 1px 0px #1A365D;
            box-shadow: 
                inset 1px 1px 0px rgba(255,255,255,0.3),
                inset -1px -1px 0px rgba(0,0,0,0.3),
                2px 2px 0px #1A365D;
        }

        .menu-button:hover {
            background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%);
            transform: translate(1px, 1px);
            box-shadow: 
                inset 1px 1px 0px rgba(255,255,255,0.3),
                inset -1px -1px 0px rgba(0,0,0,0.3),
                1px 1px 0px #1A365D;
        }

        .menu-button:active {
            transform: translate(2px, 2px);
            box-shadow: 
                inset 1px 1px 0px rgba(0,0,0,0.3),
                inset -1px -1px 0px rgba(255,255,255,0.1);
        }

        .menu-button.primary {
            background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%);
            border-color: #744210;
            text-shadow: 1px 1px 0px #744210;
            box-shadow: 
                inset 1px 1px 0px rgba(255,255,255,0.4),
                inset -1px -1px 0px rgba(0,0,0,0.3),
                2px 2px 0px #744210;
        }

        .menu-button.primary:hover {
            background: linear-gradient(145deg, #B7791F 0%, #975A16 100%);
            box-shadow: 
                inset 1px 1px 0px rgba(255,255,255,0.4),
                inset -1px -1px 0px rgba(0,0,0,0.3),
                1px 1px 0px #744210;
        }

        .menu-button.danger {
            background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%);
            border-color: #742A2A;
            text-shadow: 1px 1px 0px #742A2A;
            box-shadow: 
                inset 1px 1px 0px rgba(255,255,255,0.3),
                inset -1px -1px 0px rgba(0,0,0,0.3),
                2px 2px 0px #742A2A;
        }

        .menu-button.danger:hover {
            background: linear-gradient(145deg, #C53030 0%, #9C1C1C 100%);
            box-shadow: 
                inset 1px 1px 0px rgba(255,255,255,0.3),
                inset -1px -1px 0px rgba(0,0,0,0.3),
                1px 1px 0px #742A2A;
        }

        /* 像素风格图标 */
        .pixel-icon {
            width: 16px;
            height: 16px;
            display: inline-block;
            image-rendering: pixelated;
            position: relative;
        }

        .icon-wifi {
            background: 
                radial-gradient(circle at 8px 12px, currentColor 2px, transparent 2px),
                radial-gradient(circle at 8px 12px, currentColor 4px, transparent 4px),
                radial-gradient(circle at 8px 12px, currentColor 6px, transparent 6px);
            background-size: 16px 16px;
        }

        .icon-clock {
            border: 2px solid currentColor;
            border-radius: 50%;
            position: relative;
        }

        .icon-clock::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 50%;
            width: 1px;
            height: 4px;
            background: currentColor;
            transform: translateX(-50%);
        }

        .icon-clock::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 6px;
            width: 3px;
            height: 1px;
            background: currentColor;
            transform: translateY(-50%);
        }

        .icon-gear {
            background: 
                linear-gradient(45deg, transparent 30%, currentColor 30%, currentColor 70%, transparent 70%),
                linear-gradient(-45deg, transparent 30%, currentColor 30%, currentColor 70%, transparent 70%),
                radial-gradient(circle at center, transparent 4px, currentColor 4px, currentColor 6px, transparent 6px);
            background-size: 16px 16px;
        }

        .icon-chart {
            background: 
                linear-gradient(90deg, currentColor 2px, transparent 2px, transparent 4px, currentColor 4px, currentColor 6px, transparent 6px, transparent 8px, currentColor 8px, currentColor 10px, transparent 10px, transparent 12px, currentColor 12px, currentColor 14px, transparent 14px),
                linear-gradient(0deg, currentColor 2px, transparent 2px);
            background-size: 16px 16px;
            background-position: 0 bottom;
        }

        .icon-image {
            border: 2px solid currentColor;
            background: 
                radial-gradient(circle at 4px 4px, currentColor 2px, transparent 2px),
                linear-gradient(45deg, transparent 8px, currentColor 8px, currentColor 10px, transparent 10px);
            background-size: 16px 16px;
        }

        .icon-restart {
            background: 
                conic-gradient(from 45deg at 8px 8px, currentColor 90deg, transparent 90deg, transparent 180deg, currentColor 180deg, currentColor 270deg, transparent 270deg);
            border-radius: 50%;
        }

        .icon-restart::before {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 0;
            height: 0;
            border-left: 3px solid currentColor;
            border-top: 2px solid transparent;
            border-bottom: 2px solid transparent;
        }

        /* 页脚区域 */
        .footer {
            background: linear-gradient(145deg, #4A5568 0%, #2D3748 100%);
            padding: 20px;
            text-align: center;
            border-top: 4px solid #1A202C;
            color: #E2E8F0;
        }

        .footer-info {
            font-size: 11px;
            margin-bottom: 8px;
            font-weight: bold;
            text-shadow: 1px 1px 0px #1A202C;
        }

        .footer-version {
            font-size: 10px;
            color: #A0AEC0;
            text-shadow: 1px 1px 0px #1A202C;
        }

        /* 响应式设计 */
        @media (min-width: 768px) {
            .container {
                max-width: 500px;
            }
            
            .header h1 {
                font-size: 28px;
            }
            
            .menu-button {
                font-size: 15px;
                padding: 16px 24px;
            }
        }

        @media (max-width: 360px) {
            body {
                padding: 15px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 22px;
            }
            
            .menu-section {
                padding: 20px 15px;
            }
        }

        /* 像素风格加载动画 */
        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: currentColor;
            animation: pixelSpin 0.8s steps(4) infinite;
        }

        @keyframes pixelSpin {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(0.8); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(0.8); }
            100% { transform: rotate(360deg) scale(1); }
        }

        /* 隐藏类 */
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题区域 -->
        <div class="header">
            <h1>MARIO CLOCK</h1>
            <div class="subtitle">ESP32-C3 WiFi Portal</div>
        </div>

        <!-- WiFi状态区域 -->
        <div class="status-section">
            <div class="status-item">
                <span class="status-label">STATUS</span>
                <span class="status-value status-connected" id="connection-status">CONNECTED</span>
            </div>
            <div class="status-item">
                <span class="status-label">NETWORK</span>
                <span class="status-value" id="current-ssid">Mario_WiFi_5G</span>
            </div>
            <div class="status-item">
                <span class="status-label">IP ADDR</span>
                <span class="status-value" id="ip-address">*************</span>
            </div>
            <div class="status-item">
                <span class="status-label">SIGNAL</span>
                <div class="signal-strength">
                    <div class="signal-bars">
                        <div class="signal-bar active"></div>
                        <div class="signal-bar active"></div>
                        <div class="signal-bar active"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <span class="status-value">GOOD</span>
                </div>
            </div>
        </div>

        <!-- 功能菜单区域 -->
        <div class="menu-section">
            <button class="menu-button primary" onclick="handleMenuClick('wifi')">
                <span class="pixel-icon icon-wifi"></span>
                WIFI CONFIG
            </button>
            <button class="menu-button" onclick="handleMenuClick('clock')">
                <span class="pixel-icon icon-clock"></span>
                CLOCK SETUP
            </button>
            <button class="menu-button" onclick="handleMenuClick('device')">
                <span class="pixel-icon icon-gear"></span>
                DEVICE CONFIG
            </button>
            <button class="menu-button" onclick="handleMenuClick('stock')">
                <span class="pixel-icon icon-chart"></span>
                STOCK SETUP
            </button>
            <button class="menu-button" onclick="handleMenuClick('photo')">
                <span class="pixel-icon icon-image"></span>
                PHOTO ALBUM
            </button>
            <button class="menu-button danger" onclick="handleMenuClick('restart')">
                <span class="pixel-icon icon-restart"></span>
                RESTART
            </button>
        </div>

        <!-- 页脚区域 -->
        <div class="footer">
            <div class="footer-info">MARIO CLOCK ESP32-C3</div>
            <div class="footer-version">FIRMWARE v2.1.0 | BUILD 2024-01-15</div>
        </div>
    </div>

    <script>
        // 处理菜单点击事件
        function handleMenuClick(action) {
            const button = event.target.closest('.menu-button');
            const icon = button.querySelector('.pixel-icon');
            const originalHTML = button.innerHTML;
            
            // 添加像素风格加载效果
            button.innerHTML = `<span class="loading"></span> PROCESSING...`;
            button.style.pointerEvents = 'none';
            
            // 模拟处理延迟
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.style.pointerEvents = 'auto';
                
                // 根据不同操作执行相应逻辑
                switch(action) {
                    case 'wifi':
                        window.location.href = '/wifi-config';
                        break;
                    case 'clock':
                        window.location.href = '/clock-settings';
                        break;
                    case 'device':
                        window.location.href = '/device-config';
                        break;
                    case 'stock':
                        window.location.href = '/stock-settings';
                        break;
                    case 'photo':
                        window.location.href = '/photo-album';
                        break;
                    case 'restart':
                        if (confirm('RESTART DEVICE?\nWiFi reconnection required.')) {
                            button.innerHTML = '<span class="loading"></span> RESTARTING...';
                            fetch('/restart', { method: 'POST' })
                                .then(() => {
                                    alert('Device restarting...\nPlease reconnect WiFi.');
                                })
                                .catch(() => {
                                    button.innerHTML = originalHTML;
                                    alert('Restart failed. Please retry.');
                                });
                        }
                        break;
                }
            }, 600);
        }

        // 更新WiFi状态
        function updateWiFiStatus() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('connection-status').textContent = data.connected ? 'CONNECTED' : 'DISCONNECTED';
                    document.getElementById('connection-status').className = 
                        'status-value ' + (data.connected ? 'status-connected' : 'status-disconnected');
                    
                    if (data.connected) {
                        document.getElementById('current-ssid').textContent = data.ssid || 'UNKNOWN';
                        document.getElementById('ip-address').textContent = data.ip || '0.0.0.0';
                        
                        // 更新信号强度
                        const signalBars = document.querySelectorAll('.signal-bar');
                        const strength = Math.ceil((data.rssi + 100) / 25);
                        signalBars.forEach((bar, index) => {
                            bar.classList.toggle('active', index < strength);
                        });
                    }
                })
                .catch(error => {
                    console.log('Cannot get WiFi status:', error);
                });
        }

        // 页面加载完成后定期更新状态
        document.addEventListener('DOMContentLoaded', function() {
            updateWiFiStatus();
            setInterval(updateWiFiStatus, 10000);
        });

        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
