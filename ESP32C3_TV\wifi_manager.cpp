#include "wifi_manager.h"
#include "photo_gallery.h"

// 外部声明
extern HWCDC USBSerial;

// WifiManager类实现
WifiManager::WifiManager() : server(80) {
    // 构造函数，初始化Web服务器端口为80
    currentWifiState = WIFI_IDLE;
    wifiConnectStartTime = 0;
    connectingSSID = "";
    savedTimezone = "UTC+8";
    savedNtpServer = "ntp.aliyun.com";
    offlineModeEnabled = false;
    stockModeEnabled = false;
    galleryModeEnabled = false;
    photoGallery = nullptr;
    // 初始化股票代码数组
    for (int i = 0; i < 4; i++) {
        stockCodes[i] = "";
    }
    savedBrightness = 128;
    savedOrientation = "usb_down";
    ntpAutoSyncCallback = nullptr;
    modeChangeCallback = nullptr;

    // 初始化状态记录
    initialWifiConnected = false;
    initialNtpSynced = false;
    initializationComplete = false;

    // 最终系统模式
    finalAutoMode = false;
    finalWifiSSID = "";
    finalWifiIP = "";

    // 初始化异步WiFi扫描状态
    scanState = SCAN_IDLE;
    scanStartTime = 0;
    cachedScanResult = "";
}

void WifiManager::init() {
    USBSerial.println("=== WiFi Manager Starting ===");

    // 加载设备设置
    loadDeviceSettings();

    // 加载时钟设置
    loadClockSettings();

    // 加载离线模式设置
    loadOfflineMode();

    // 加载股票设置
    loadStockSettings();

    // 加载相册设置
    loadGallerySettings();

    // 先设置AP模式
    setupAP();

    // 立即设置DNS服务器和Web服务器，确保Captive Portal快速响应
    setupDNS();
    setupWebServer();

    // 标记初始化完成（让AP和Web服务器立即可用）
    initializationComplete = true;

    USBSerial.println("WiFi Manager basic initialization complete - AP and Web server ready");

    // 进行一次性WiFi连接和NTP同步判断，确定最终系统模式
    performSynchronousInitialization();

    // 根据初始化结果确定最终系统模式
    if (initialWifiConnected && initialNtpSynced) {
        finalAutoMode = true;
        finalWifiSSID = WiFi.SSID();
        finalWifiIP = WiFi.localIP().toString();
        USBSerial.println("=== 最终系统模式：自动模式 ===");
        USBSerial.print("WiFi SSID: ");
        USBSerial.println(finalWifiSSID);
        USBSerial.print("WiFi IP: ");
        USBSerial.println(finalWifiIP);
    } else {
        finalAutoMode = false;
        finalWifiSSID = "";
        finalWifiIP = "";
        USBSerial.println("=== 最终系统模式：离线模式 ===");

        // 确保WiFi模式是AP+STA，以便WiFi扫描功能正常工作
        WiFi.mode(WIFI_AP_STA);
        delay(100);
    }

    USBSerial.println("WiFi Manager fully initialized");
}



void WifiManager::performSynchronousInitialization() {
    USBSerial.println("=== Starting Synchronous Initialization ===");

    // 重置初始化状态
    initialWifiConnected = false;
    initialNtpSynced = false;

    // 尝试加载并同步连接WiFi
    preferences.begin("wifi", true);
    String savedSSID = preferences.getString("ssid", "");
    String savedPassword = preferences.getString("password", "");
    preferences.end();

    if (savedSSID.length() > 0) {
        USBSerial.println("Found saved WiFi credentials, attempting synchronous connection...");
        USBSerial.print("SSID: ");
        USBSerial.println(savedSSID);

        // 开始WiFi连接
        WiFi.begin(savedSSID.c_str(), savedPassword.c_str());

        // 同步等待WiFi连接结果（最多10秒）
        int attempts = 0;
        const int maxAttempts = 20; // 10秒，每次等待500ms

        while (WiFi.status() != WL_CONNECTED && attempts < maxAttempts) {
            delay(500);
            attempts++;
            USBSerial.print(".");

            // 检查是否明确失败
            wl_status_t status = WiFi.status();
            if (isQuickWiFiFailure(status)) {
                USBSerial.println();
                USBSerial.println("WiFi connection failed with error status");
                break;
            }
        }

        if (WiFi.status() == WL_CONNECTED) {
            USBSerial.println();
            USBSerial.println("WiFi connected successfully");
            USBSerial.print("IP address: ");
            USBSerial.println(WiFi.localIP());
            initialWifiConnected = true;

            // 如果WiFi连接成功且未开启离线模式，尝试NTP同步
            if (!offlineModeEnabled) {
                USBSerial.println("Attempting synchronous NTP sync...");
                delay(2000); // 等待连接稳定

                if (performSynchronousNTPSync()) {
                    initialNtpSynced = true;
                    USBSerial.println("NTP synchronization completed successfully");
                } else {
                    USBSerial.println("NTP synchronization failed");
                }
            } else {
                USBSerial.println("Offline mode enabled, skipping NTP sync");
                initialNtpSynced = true; // 离线模式下认为NTP"成功"（不需要同步）
            }
        } else {
            USBSerial.println();
            USBSerial.println("WiFi connection failed or timed out");
        }
    } else {
        USBSerial.println("No saved WiFi credentials found");
    }

    USBSerial.print("Initialization results - WiFi: ");
    USBSerial.print(initialWifiConnected ? "SUCCESS" : "FAILED");
    USBSerial.print(", NTP: ");
    USBSerial.println(initialNtpSynced ? "SUCCESS" : "FAILED");
    USBSerial.println("=== Synchronous Initialization Complete ===");
}

bool WifiManager::performSynchronousNTPSync() {
    // 检查WiFi连接状态
    if (WiFi.status() != WL_CONNECTED) {
        USBSerial.println("WiFi not connected, skipping NTP sync");
        return false;
    }

    if (savedNtpServer.length() == 0) {
        USBSerial.println("No NTP server configured, skipping sync");
        return false;
    }

    USBSerial.print("Syncing time from NTP server: ");
    USBSerial.println(savedNtpServer);
    USBSerial.print("Using timezone: ");
    USBSerial.println(savedTimezone);

    // 计算时区偏移
    int timezoneOffset = 0;
    if (savedTimezone.startsWith("UTC+")) {
        timezoneOffset = savedTimezone.substring(4).toInt() * 3600;
    } else if (savedTimezone.startsWith("UTC-")) {
        timezoneOffset = -savedTimezone.substring(4).toInt() * 3600;
    }

    USBSerial.print("Timezone offset: ");
    USBSerial.print(timezoneOffset);
    USBSerial.println(" seconds");

    // 配置NTP，直接设置时区偏移
    configTime(timezoneOffset, 0, savedNtpServer.c_str());

    // 等待时间同步（最多10次尝试，每次500ms，总共5秒）
    int attempts = 0;
    time_t now = 0;
    while (now < 1000000000 && attempts < 10) {
        delay(500);
        time(&now);
        attempts++;
        USBSerial.print(".");
    }

    if (now > 1000000000) {
        // 等待时区应用
        delay(1000);

        // 获取当前时间并设置到马里奥时钟
        struct tm timeinfo;
        if (getLocalTime(&timeinfo)) {
            extern int currentHour;
            extern int currentMinute;
            currentHour = timeinfo.tm_hour;
            currentMinute = timeinfo.tm_min;

            USBSerial.println();
            USBSerial.print("Time synchronized successfully: ");
            USBSerial.print(currentHour);
            USBSerial.print(":");
            USBSerial.print(currentMinute < 10 ? "0" : "");
            USBSerial.println(currentMinute);

            return true;
        }
    }

    USBSerial.println();
    USBSerial.println("NTP synchronization failed or timed out");
    return false;
}

bool WifiManager::isQuickWiFiFailure(wl_status_t status) {
    return (status == WL_CONNECT_FAILED ||
            status == WL_CONNECTION_LOST ||
            status == WL_NO_SSID_AVAIL);
}

void WifiManager::resetWiFiForScan() {
    USBSerial.println("Resetting WiFi for scan functionality...");

    // 强制断开当前连接（包括清除保存的凭据）
    WiFi.disconnect(true);
    delay(100);

    // 完全重置WiFi模式
    WiFi.mode(WIFI_OFF);
    delay(100);
    WiFi.mode(WIFI_AP_STA);
    delay(200);

    // 重新启动AP
    if (WiFi.softAP("mario-clock")) {
        USBSerial.println("AP restarted successfully for scan");
    } else {
        USBSerial.println("Failed to restart AP for scan");
    }

    // 重新配置AP的IP地址（确保一致性）
    IPAddress apIP(192, 168, 4, 1);
    IPAddress gateway(192, 168, 4, 1);
    IPAddress subnet(255, 255, 255, 0);
    WiFi.softAPConfig(apIP, gateway, subnet);

    USBSerial.println("WiFi reset completed for scan functionality");
}

void WifiManager::setupAP() {
    // 设置AP模式的SSID
    String apSSID = "mario-clock";

    USBSerial.print("Starting AP mode with SSID: ");
    USBSerial.println(apSSID);

    // 直接设置AP模式，不使用WiFiManager的autoConnect
    // 这样可以避免WiFiManager的默认Web界面干扰
    WiFi.mode(WIFI_AP_STA);

    // 配置AP的IP地址
    IPAddress apIP(192, 168, 4, 1);
    IPAddress gateway(192, 168, 4, 1);
    IPAddress subnet(255, 255, 255, 0);
    WiFi.softAPConfig(apIP, gateway, subnet);

    // 启动AP
    if (WiFi.softAP(apSSID.c_str())) {
        USBSerial.println("AP mode started successfully");
        USBSerial.print("AP IP address: ");
        USBSerial.println(WiFi.softAPIP());
    } else {
        USBSerial.println("Failed to start AP mode");
    }

    // 尝试连接到保存的WiFi（如果有的话）
    // 这部分逻辑移到loadWifiCredentials()中处理
}

void WifiManager::setupDNS() {
    // 启动DNS服务器，将所有域名解析到AP的IP地址
    const byte DNS_PORT = 53;
    dnsServer.start(DNS_PORT, "*", WiFi.softAPIP());
    USBSerial.println("DNS server started for Captive Portal");
}

void WifiManager::setupWebServer() {
    // 设置根路径处理函数
    server.on("/", [this]() {
        handleRoot();
    });

    // 设置离线模式页面
    server.on("/offline", [this]() {
        handleOfflineMode();
    });

    // 设置时间设置处理
    server.on("/settime", HTTP_POST, [this]() {
        handleSetTime();
    });

    // 设置WiFi配置页面
    server.on("/wificonfig", [this]() {
        handleWifiConfig();
    });

    // 设置WiFi扫描处理
    server.on("/wifiscan", [this]() {
        handleWifiScan();
    });

    // 设置WiFi连接处理
    server.on("/wificonnect", HTTP_POST, [this]() {
        handleWifiConnect();
    });

    // 设置WiFi状态查询
    server.on("/wifistatus", [this]() {
        handleWifiStatus();
    });

    // 设置时钟设置页面
    server.on("/clocksetting", [this]() {
        handleClockSetting();
    });

    // 设置时间同步处理
    server.on("/timesync", HTTP_POST, [this]() {
        handleTimeSync();
    });

    // 设置离线模式开关处理
    server.on("/offlinemode", HTTP_POST, [this]() {
        handleOfflineModeToggle();
    });

    // 设置重启设备处理
    server.on("/restart", HTTP_POST, [this]() {
        handleRestart();
    });

    // 设置重启设备页面（GET请求）
    server.on("/restart", HTTP_GET, [this]() {
        handleRestartPage();
    });

    // 设置设备配置页面
    server.on("/deviceconfig", [this]() {
        handleDeviceConfig();
    });

    // 设置设备设置处理
    server.on("/devicesettings", HTTP_POST, [this]() {
        handleDeviceSettings();
    });

    // 设置亮度测试处理
    server.on("/brightnesstest", HTTP_POST, [this]() {
        handleBrightnessTest();
    });

    // 设置股票配置页面
    server.on("/stockconfig", [this]() {
        handleStockConfig();
    });

    // 设置股票设置处理
    server.on("/stocksettings", HTTP_POST, [this]() {
        handleStockSettings();
    });

    // 设置电子相册页面
    server.on("/gallery", [this]() {
        handleGallery();
    });

    // 设置相册设置处理
    server.on("/gallerysettings", HTTP_POST, [this]() {
        handleGallerySettings();
    });

    // 设置照片上传处理
    server.on("/photoupload", HTTP_POST, [this]() {
        // 上传完成后的处理
        // 这里需要检查上传结果，但由于静态变量作用域问题，暂时返回成功
        server.send(200, "application/json", "{\"status\":\"success\",\"message\":\"照片上传成功\"}");
    }, [this]() {
        // 文件上传过程处理
        handlePhotoUpload();
    });

    // 设置照片删除处理
    server.on("/photodelete", HTTP_POST, [this]() {
        handlePhotoDelete();
    });

    // 设置Captive Portal相关路径 - 覆盖所有主要操作系统的检测路径

    // Android系统检测路径
    server.on("/generate_204", [this]() {
        handleRoot();
    });

    server.on("/gen_204", [this]() {
        handleRoot();
    });

    // Windows系统检测路径
    server.on("/fwlink", [this]() {
        handleRoot();
    });

    server.on("/connecttest.txt", [this]() {
        handleRoot();
    });

    server.on("/ncsi.txt", [this]() {
        handleRoot();
    });

    // iOS/macOS系统检测路径
    server.on("/hotspot-detect.html", [this]() {
        handleRoot();
    });

    server.on("/library/test/success.html", [this]() {
        handleRoot();
    });

    server.on("/captive.apple.com/hotspot-detect.html", [this]() {
        handleRoot();
    });

    // 其他常见检测路径
    server.on("/check_network_status.txt", [this]() {
        handleRoot();
    });

    server.on("/redirect", [this]() {
        handleRoot();
    });

    server.on("/success.txt", [this]() {
        handleRoot();
    });

    // 捕获所有其他请求
    server.onNotFound([this]() {
        handleRoot();
    });

    // 启动Web服务器
    server.begin();
    USBSerial.println("Web server started on port 80 with Captive Portal");
}

void WifiManager::handleRoot() {
    // 检查是否是Captive Portal检测请求
    String path = server.uri();
    String userAgent = server.header("User-Agent");

    USBSerial.print("Captive Portal request - Path: ");
    USBSerial.print(path);
    USBSerial.print(", User-Agent: ");
    USBSerial.println(userAgent);

    // 对于特定的检测路径，返回适当的响应来触发强制门户
    if (path == "/generate_204" || path == "/gen_204" || path == "/gstatic.com/generate_204") {
        // Android检测 - 返回非204状态码来触发强制门户
        server.sendHeader("Location", "http://***********/", true);
        server.send(302, "text/html", "<html><head><meta http-equiv='refresh' content='0;url=http://***********/'></head><body>Redirecting...</body></html>");
        return;
    }

    if (path == "/connecttest.txt" || path == "/ncsi.txt") {
        // Windows检测 - 返回错误内容来触发强制门户
        server.send(200, "text/plain", "Microsoft Connect Test");
        return;
    }

    if (path == "/hotspot-detect.html" || path.indexOf("captive.apple.com") >= 0) {
        // iOS/macOS检测 - 返回非Success内容来触发强制门户
        server.sendHeader("Location", "http://***********/", true);
        server.send(302, "text/html", "<html><head><meta http-equiv='refresh' content='0;url=http://***********/'></head><body>Redirecting...</body></html>");
        return;
    }

    // 创建马里奥风格的强制门户主页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='apple-mobile-web-app-capable' content='yes'>";
    html += "<meta name='apple-mobile-web-app-status-bar-style' content='black'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<meta http-equiv='Cache-Control' content='no-cache, no-store, must-revalidate'>";
    html += "<meta http-equiv='Pragma' content='no-cache'>";
    html += "<meta http-equiv='Expires' content='0'>";
    html += "<title>马里奥时钟 - WiFi Portal</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 400px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #B91C1C; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; margin-bottom: 5px; position: relative; z-index: 1; text-shadow: 2px 2px 0px #8B0000; letter-spacing: 1px; }";
    html += ".header .subtitle { font-size: 12px; opacity: 0.9; position: relative; z-index: 1; text-shadow: 1px 1px 0px #8B0000; }";
    html += ".status-section { background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); padding: 20px; border-bottom: 4px solid #1A365D; position: relative; }";
    html += ".status-section::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: repeating-linear-gradient(90deg, transparent 0px, transparent 18px, rgba(0,0,0,0.1) 18px, rgba(0,0,0,0.1) 20px); pointer-events: none; }";
    html += ".status-item { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; position: relative; z-index: 1; }";
    html += ".status-item:last-child { margin-bottom: 0; }";
    html += ".status-label { font-size: 13px; color: white; font-weight: bold; text-shadow: 1px 1px 0px #1A365D; }";
    html += ".status-value { font-size: 13px; color: #F7FAFC; font-weight: bold; background: rgba(0,0,0,0.2); padding: 2px 6px; border-radius: 2px; }";
    html += ".status-connected { color: #68D391; background: rgba(104, 211, 145, 0.2); }";
    html += ".status-disconnected { color: #FC8181; background: rgba(252, 129, 129, 0.2); }";
    html += ".menu-section { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; position: relative; }";
    html += ".menu-button { display: flex; align-items: center; justify-content: center; gap: 10px; width: 100%; max-width: 300px; margin: 0 auto 15px auto; padding: 15px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:active { transform: translate(2px, 2px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.3), inset -1px -1px 0px rgba(255,255,255,0.1); }";
    html += ".menu-button.primary { background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%); border-color: #744210; text-shadow: 1px 1px 0px #744210; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #744210; }";
    html += ".menu-button.primary:hover { background: linear-gradient(145deg, #B7791F 0%, #975A16 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #744210; }";
    html += ".menu-button.danger { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); border-color: #742A2A; text-shadow: 1px 1px 0px #742A2A; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #742A2A; }";
    html += ".menu-button.danger:hover { background: linear-gradient(145deg, #C53030 0%, #9C1C1C 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #742A2A; }";
    html += ".footer { background: linear-gradient(145deg, #4A5568 0%, #2D3748 100%); padding: 20px; text-align: center; border-top: 4px solid #1A202C; color: #E2E8F0; }";
    html += ".footer-info { font-size: 11px; margin-bottom: 8px; font-weight: bold; text-shadow: 1px 1px 0px #1A202C; }";
    html += ".footer-version { font-size: 10px; color: #A0AEC0; text-shadow: 1px 1px 0px #1A202C; }";
    html += "@media (min-width: 768px) { .container { max-width: 500px; } .header h1 { font-size: 28px; } .menu-button { font-size: 15px; padding: 16px 24px; } }";
    html += "@media (max-width: 360px) { body { padding: 15px; } .header { padding: 20px 15px; } .header h1 { font-size: 22px; } .menu-section { padding: 20px 15px; } }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>MARIO CLOCK</h1>";
    html += "</div>";
    html += "<div class='status-section'>";
    html += "<div class='status-item'>";
    html += "<span class='status-label'>连接状态</span>";
    html += "<span class='status-value' id='connection-status'>检查中...</span>";
    html += "</div>";
    html += "<div class='status-item'>";
    html += "<span class='status-label'>网络名称</span>";
    html += "<span class='status-value' id='current-ssid'>mario-clock</span>";
    html += "</div>";
    html += "<div class='status-item'>";
    html += "<span class='status-label'>IP地址</span>";
    html += "<span class='status-value' id='ip-address'>***********</span>";
    html += "</div>";
    html += "</div>";
    html += "<div class='menu-section'>";
    html += "<a href='/offline' class='menu-button'>离线模式</a>";
    html += "<a href='/wificonfig' class='menu-button primary'>配置WIFI</a>";
    html += "<a href='/clocksetting' class='menu-button'>时钟设置</a>";
    html += "<a href='/deviceconfig' class='menu-button'>设备配置</a>";
    html += "<a href='/stockconfig' class='menu-button'>股票设置</a>";
    html += "<a href='/gallery' class='menu-button'>电子相册</a>";
    html += "<a href='/restart' class='menu-button danger'>重启设备</a>";
    html += "</div>";
    html += "<div class='footer'>";
    html += "<div class='footer-info'>马里奥时钟</div>";
    html += "<div class='footer-version'>FIRMWARE v1.0.0 | BUILD 2025-07-16</div>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "function updateWifiStatus() {";
    html += "  fetch('/wifistatus')";
    html += "    .then(response => response.json())";
    html += "    .then(data => {";
    html += "      const connectionStatus = document.getElementById('connection-status');";
    html += "      const currentSSID = document.getElementById('current-ssid');";
    html += "      const ipAddress = document.getElementById('ip-address');";
    html += "      if (data.status === 'connected') {";
    html += "        connectionStatus.textContent = '已连接';";
    html += "        connectionStatus.className = 'status-value status-connected';";
    html += "        currentSSID.textContent = data.ssid || '未知';";
    html += "        ipAddress.textContent = data.ip || '0.0.0.0';";
    html += "      } else if (data.status === 'offline') {";
    html += "        connectionStatus.textContent = '离线模式';";
    html += "        connectionStatus.className = 'status-value status-disconnected';";
    html += "        currentSSID.textContent = 'mario-clock';";
    html += "        ipAddress.textContent = '***********';";
    html += "      } else {";
    html += "        connectionStatus.textContent = '未连接';";
    html += "        connectionStatus.className = 'status-value status-disconnected';";
    html += "        currentSSID.textContent = 'mario-clock';";
    html += "        ipAddress.textContent = '***********';";
    html += "      }";
    html += "    })";
    html += "    .catch(error => {";
    html += "      const connectionStatus = document.getElementById('connection-status');";
    html += "      const currentSSID = document.getElementById('current-ssid');";
    html += "      const ipAddress = document.getElementById('ip-address');";
    html += "      connectionStatus.textContent = '错误';";
    html += "      connectionStatus.className = 'status-value status-disconnected';";
    html += "      currentSSID.textContent = 'mario-clock';";
    html += "      ipAddress.textContent = '***********';";
    html += "    });";
    html += "}";

    html += "updateWifiStatus();";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    // 设置强制门户相关的HTTP头
    server.sendHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    server.sendHeader("Pragma", "no-cache");
    server.sendHeader("Expires", "0");
    server.sendHeader("Content-Type", "text/html; charset=utf-8");

    // 发送HTML响应
    server.send(200, "text/html", html);

    USBSerial.print("Served captive portal page to client: ");
    USBSerial.println(path);
}

void WifiManager::handleOfflineMode() {
    // 创建离线模式时间设置页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<title>离线模式 - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 450px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #B91C1C; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; position: relative; z-index: 1; text-shadow: 2px 2px 0px #8B0000; letter-spacing: 1px; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; position: relative; }";
    html += ".form-group { margin-bottom: 25px; text-align: left; position: relative; z-index: 1; }";
    html += ".form-group > label:first-child { display: block; margin-bottom: 12px; font-weight: bold; color: #2D3748; font-size: 16px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group input[type='number'] { width: 100%; padding: 12px; border: 3px solid #4A5568; border-radius: 0; font-size: 14px; font-family: 'Courier New', monospace; font-weight: bold; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); color: #2D3748; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group input[type='number']:focus { border-color: #D69E2E; outline: none; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8), 0 0 0 2px rgba(214, 158, 46, 0.3); }";
    html += ".switch { position: relative; display: inline-block; width: 70px; height: 40px; margin-top: 8px; }";
    html += ".switch input { opacity: 0; width: 0; height: 0; }";
    html += ".slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); transition: .3s; border: 3px solid #4A5568; border-radius: 0; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #2D3748; }";
    html += ".slider:before { position: absolute; content: ''; height: 28px; width: 28px; left: 4px; bottom: 4px; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); transition: .3s; border: 2px solid #4A5568; border-radius: 0; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.2); }";
    html += "input:checked + .slider { background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); border-color: #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += "input:checked + .slider:before { transform: translateX(26px); background: linear-gradient(145deg, #FFF5B7 0%, #FED7AA 100%); border-color: #D69E2E; }";
    html += ".description { color: #4A5568; font-size: 12px; margin-top: 8px; line-height: 1.4; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; }";
    html += ".description.warning { color: #D69E2E; font-weight: bold; background: rgba(214, 158, 46, 0.1); padding: 8px; border: 2px solid #D69E2E; margin-top: 12px; border-radius: 0; }";
    html += ".menu-button { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 120px; margin: 8px 4px; padding: 12px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:active { transform: translate(2px, 2px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.3), inset -1px -1px 0px rgba(255,255,255,0.1); }";
    html += ".menu-button.primary { background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%); border-color: #744210; text-shadow: 1px 1px 0px #744210; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #744210; }";
    html += ".menu-button.primary:hover { background: linear-gradient(145deg, #B7791F 0%, #975A16 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #744210; }";
    html += ".button-group { text-align: center; margin-top: 25px; position: relative; z-index: 1; }";
    html += "form { max-width: 350px; margin: 0 auto; position: relative; z-index: 1; }";
    html += "@media (min-width: 768px) { .container { max-width: 550px; } .header h1 { font-size: 28px; } .menu-button { font-size: 15px; padding: 14px 24px; } }";
    html += "@media (max-width: 360px) { body { padding: 15px; } .header { padding: 20px 15px; } .header h1 { font-size: 22px; } .content { padding: 20px 15px; } }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>离线模式</h1>";
    html += "</div>";
    html += "<div class='content'>";

    // 离线模式开关
    html += "<div class='form-group'>";
    html += "<label>开启离线模式</label>";
    html += "<label class='switch'>";
    html += "<input type='checkbox' id='offlineModeSwitch' " + String(offlineModeEnabled ? "checked" : "") + " onchange='toggleOfflineMode(this.checked)'>";
    html += "<span class='slider'></span>";
    html += "</label>";
    html += "<div class='description'>开启后，系统启动时不会自动连接NTP服务器</div>";
    html += "<div class='description'>开启离线模式：需要手动设置时间，系统将使用您设置的时间运行</div>";
    html += "<div class='description'>关闭离线模式：直接点击确认即可，系统将在下次启动时自动从NTP服务器同步时间</div>";
    html += "<div class='description warning'>注意：关闭离线模式时无需输入时间，系统会自动切换到网络时间同步模式</div>";
    html += "</div>";

    html += "<form action='/settime' method='POST'>";
    html += "<div class='form-group'>";
    html += "<label for='hour'>小时 (0-23):</label>";
    html += "<input type='number' id='hour' name='hour' min='0' max='23' " + String(offlineModeEnabled ? "required" : "") + ">";
    html += "</div>";
    html += "<div class='form-group'>";
    html += "<label for='minute'>分钟 (0-59):</label>";
    html += "<input type='number' id='minute' name='minute' min='0' max='59' " + String(offlineModeEnabled ? "required" : "") + ">";
    html += "</div>";
    html += "<div class='button-group'>";
    html += "<button type='submit' class='menu-button primary'>确认</button>";
    html += "<a href='/' class='menu-button'>返回</a>";
    html += "</div>";
    html += "</form>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "function toggleOfflineMode(enabled) {";
    html += "  var hourInput = document.getElementById('hour');";
    html += "  var minuteInput = document.getElementById('minute');";
    html += "  if (enabled) {";
    html += "    hourInput.setAttribute('required', '');";
    html += "    minuteInput.setAttribute('required', '');";
    html += "  } else {";
    html += "    hourInput.removeAttribute('required');";
    html += "    minuteInput.removeAttribute('required');";
    html += "  }";
    html += "  fetch('/offlinemode', {";
    html += "    method: 'POST',";
    html += "    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },";
    html += "    body: 'enabled=' + (enabled ? '1' : '0')";
    html += "  })";
    html += "  .then(response => response.text())";
    html += "  .then(data => {";
    html += "    console.log('Offline mode setting saved');";
    html += "  })";
    html += "  .catch(error => {";
    html += "    console.error('Error saving offline mode setting:', error);";
    html += "  });";
    html += "}";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Served offline mode page");
}

void WifiManager::handleSetTime() {
    // 防止重复处理 - 添加静态变量来跟踪处理状态
    static unsigned long lastProcessTime = 0;
    unsigned long currentTime = millis();

    // 如果距离上次处理不到1秒，忽略这次请求
    if (currentTime - lastProcessTime < 1000) {
        USBSerial.println("Ignoring duplicate settime request");
        server.send(200, "text/plain", "Request ignored - too frequent");
        return;
    }
    lastProcessTime = currentTime;

    // 获取外部时间变量的引用
    extern int currentHour;
    extern int currentMinute;

    // 获取表单数据
    String hourStr = server.arg("hour");
    String minuteStr = server.arg("minute");

    // 如果离线模式关闭，直接应用设置而不验证时间输入
    if (!offlineModeEnabled) {
        USBSerial.println("Offline mode disabled, applying settings without time input");
    } else {
        // 离线模式开启时，验证时间输入
        if (hourStr.length() == 0 || minuteStr.length() == 0) {
            // 输入无效，返回错误页面
            String html = "<!DOCTYPE html>";
            html += "<html lang='zh-CN'>";
            html += "<head><meta charset='UTF-8'><title>错误</title></head>";
            html += "<body><h1>输入错误</h1><p>请输入有效的时间</p>";
            html += "<a href='/offline'>返回设置</a></body></html>";
            server.send(400, "text/html", html);
            return;
        }

        int hour = hourStr.toInt();
        int minute = minuteStr.toInt();

        // 验证时间范围
        if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
            // 时间范围无效
            String html = "<!DOCTYPE html>";
            html += "<html lang='zh-CN'>";
            html += "<head><meta charset='UTF-8'><title>错误</title></head>";
            html += "<body><h1>时间范围错误</h1><p>小时应为0-23，分钟应为0-59</p>";
            html += "<a href='/offline'>返回设置</a></body></html>";
            server.send(400, "text/html", html);
            return;
        }

        // 设置时间
        currentHour = hour;
        currentMinute = minute;

        USBSerial.print("Time set to: ");
        USBSerial.print(currentHour);
        USBSerial.print(":");
        USBSerial.println(currentMinute);
    }

    // 创建成功页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<title>设置成功 - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 450px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #1A365D; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; position: relative; z-index: 1; text-shadow: 2px 2px 0px #1A365D; letter-spacing: 1px; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; text-align: center; position: relative; }";
    html += ".success-msg { font-size: 16px; color: #2F855A; margin-bottom: 20px; line-height: 1.5; font-weight: bold; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; position: relative; z-index: 1; }";
    html += ".time-display { font-size: 28px; font-weight: bold; color: #2D3748; margin: 25px 0; padding: 20px; background: linear-gradient(145deg, #FFF5B7 0%, #FED7AA 100%); border: 3px solid #D69E2E; border-radius: 0; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.2), 2px 2px 0px #B7791F; font-family: 'Courier New', monospace; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); position: relative; z-index: 1; }";
    html += ".menu-button { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 120px; margin: 8px 4px; padding: 12px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; z-index: 1; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".button-group { text-align: center; margin-top: 25px; position: relative; z-index: 1; }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>设置成功</h1>";
    html += "</div>";
    html += "<div class='content'>";

    if (offlineModeEnabled) {
        html += "<div class='success-msg'>时间设置成功，正在进入马里奥时钟离线模式</div>";
        html += "<div class='time-display'>";
        html += String(currentHour < 10 ? "0" : "") + String(currentHour) + ":";
        html += String(currentMinute < 10 ? "0" : "") + String(currentMinute);
        html += "</div>";
    } else {
        html += "<div class='success-msg'>离线模式已关闭，设备将自动重启以应用新设置</div>";
        html += "<div class='success-msg'>重启后系统将自动连接NTP服务器同步时间</div>";
    }
    html += "<div class='button-group'>";
    if (!offlineModeEnabled) {
        // 马里奥主题的重启倒计时显示
        html += "<div class='restart-container'>";
        html += "<div class='restart-icon'>🔄</div>";
        html += "<div class='restart-msg'>设备将在 <span id='countdown'>5</span> 秒后自动重启</div>";
        html += "<div class='restart-submsg'>请稍候，正在准备重启...</div>";
        html += "</div>";

        // 添加重启相关的CSS样式
        html += "<style>";
        html += ".restart-container { margin: 25px 0; padding: 25px; background: linear-gradient(145deg, #FF6B6B 0%, #E53E3E 100%); border: 4px solid #C53030; border-radius: 0; box-shadow: inset 2px 2px 0px rgba(255,255,255,0.3), inset -2px -2px 0px rgba(0,0,0,0.3), 4px 4px 0px #9B2C2C; position: relative; animation: restartPulse 2s ease-in-out infinite; }";
        html += "@keyframes restartPulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.02); } }";
        html += ".restart-container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
        html += ".restart-icon { font-size: 48px; margin-bottom: 15px; animation: spin 2s linear infinite; position: relative; z-index: 1; }";
        html += "@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }";
        html += ".restart-msg { font-size: 18px; font-weight: bold; color: white; margin-bottom: 10px; text-shadow: 2px 2px 0px #9B2C2C; font-family: 'Courier New', monospace; position: relative; z-index: 1; }";
        html += ".restart-submsg { font-size: 14px; color: rgba(255,255,255,0.9); text-shadow: 1px 1px 0px #9B2C2C; font-family: 'Courier New', monospace; position: relative; z-index: 1; }";
        html += "#countdown { font-size: 24px; font-weight: bold; color: #FFF5B7; text-shadow: 2px 2px 0px #9B2C2C; }";
        html += "</style>";
    } else {
        html += "<a href='/' class='menu-button'>返回首页</a>";
    }
    html += "</div>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";

    // 如果切换到自动模式，添加马里奥主题的重启倒计时脚本
    if (!offlineModeEnabled) {
        html += "let countdownValue = 5;";
        html += "const countdownElement = document.getElementById('countdown');";
        html += "const countdownTimer = setInterval(function() {";
        html += "  countdownValue--;";
        html += "  if (countdownElement) countdownElement.textContent = countdownValue;";
        html += "  if (countdownValue <= 0) {";
        html += "    clearInterval(countdownTimer);";
        html += "    // 倒计时结束，设备即将重启";
        html += "  }";
        html += "}, 1000);";


    }

    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Time setting completed successfully");

    // 如果切换到自动模式，延迟重启设备
    if (!offlineModeEnabled) {
        USBSerial.println("Offline mode disabled - device will restart in 5 seconds");
        delay(5000);  // 等待5秒确保响应发送完成
        USBSerial.println("Restarting device now...");
        ESP.restart();
    }
}

void WifiManager::handleOfflineModeToggle() {
    // 获取表单数据
    String enabledStr = server.arg("enabled");

    // 转换为布尔值
    bool enabled = (enabledStr == "1");

    // 保存设置
    saveOfflineMode(enabled);

    // 处理WiFi连接状态
    if (enabled) {
        // 切换到离线模式：断开WiFi并禁用重试
        WiFi.disconnect();
        wifiRetryDisabled = true;
        currentWifiState = WIFI_IDLE;
        USBSerial.println("Offline mode enabled - WiFi disconnected and retry disabled");
    } else {
        // 切换到在线模式：简单重置状态，等待重启
        wifiRetryDisabled = false;
        currentWifiState = WIFI_IDLE;
        USBSerial.println("Offline mode disabled - device will restart after user confirmation");
    }

    // 返回成功响应
    server.send(200, "text/plain", "OK");

    USBSerial.print("Offline mode setting changed to: ");
    USBSerial.println(enabled ? "enabled" : "disabled");
}

void WifiManager::handleRestart() {
    USBSerial.println("=== RESTART REQUEST RECEIVED ===");

    // 返回成功响应
    server.send(200, "text/plain", "OK");

    USBSerial.println("Device restart requested via web interface");
    USBSerial.println("Sending response and preparing to restart...");

    // 延迟一点时间确保响应发送完成，然后重启
    delay(1000);

    USBSerial.println("Calling ESP.restart() now...");
    ESP.restart();
}

void WifiManager::handleRestartPage() {
    // 创建重启确认页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<title>重启设备 - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; display: flex; align-items: center; justify-content: center; }";
    html += ".container { max-width: 400px; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; box-shadow: 4px 4px 0px #3E2723; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 20px; border-bottom: 4px solid #B91C1C; }";
    html += ".header h1 { font-size: 20px; font-weight: bold; text-shadow: 2px 2px 0px #8B0000; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 30px 20px; text-align: center; }";
    html += ".warning { color: #D69E2E; font-size: 16px; font-weight: bold; margin-bottom: 20px; }";
    html += ".menu-button { display: inline-block; margin: 10px; padding: 12px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; font-size: 14px; font-weight: bold; cursor: pointer; text-shadow: 1px 1px 0px #1A365D; box-shadow: 2px 2px 0px #1A365D; }";
    html += ".menu-button.danger { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); border-color: #B91C1C; text-shadow: 1px 1px 0px #B91C1C; box-shadow: 2px 2px 0px #B91C1C; }";
    html += ".menu-button:hover { transform: translate(1px, 1px); box-shadow: 1px 1px 0px #1A365D; }";
    html += ".menu-button.danger:hover { box-shadow: 1px 1px 0px #B91C1C; }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>重启设备</h1>";
    html += "</div>";
    html += "<div class='content'>";
    html += "<div class='warning'>确定要重启设备吗？</div>";
    html += "<p>重启后需要重新连接WiFi</p>";
    html += "<br>";
    html += "<button class='menu-button danger' onclick='confirmRestart()'>确认重启</button>";
    html += "<a href='/' class='menu-button'>取消</a>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "function confirmRestart() {";
    html += "  const button = event.target;";
    html += "  button.textContent = '重启中...';";
    html += "  button.disabled = true;";
    html += "  fetch('/restart', { method: 'POST' })";
    html += "    .then(() => {";
    html += "      alert('设备正在重启...\\n请稍后重新连接WiFi。');";
    html += "    })";
    html += "    .catch(() => {";
    html += "      alert('重启失败，请重试。');";
    html += "      button.textContent = '确认重启';";
    html += "      button.disabled = false;";
    html += "    });";
    html += "}";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Served restart confirmation page");
}

void WifiManager::handleClockSetting() {
    // 创建时钟设置页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<title>时钟设置 - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 550px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #B91C1C; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; position: relative; z-index: 1; text-shadow: 2px 2px 0px #8B0000; letter-spacing: 1px; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; position: relative; }";
    html += ".form-group { margin-bottom: 25px; text-align: left; position: relative; z-index: 1; }";
    html += ".form-group label { display: block; margin-bottom: 12px; font-weight: bold; color: #2D3748; font-size: 16px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group select, .form-group input[type='text'] { width: 100%; padding: 12px; border: 3px solid #4A5568; border-radius: 0; font-size: 14px; font-family: 'Courier New', monospace; font-weight: bold; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); color: #2D3748; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group select:focus, .form-group input[type='text']:focus { border-color: #D69E2E; outline: none; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8), 0 0 0 2px rgba(214, 158, 46, 0.3); }";
    html += ".example-text { font-size: 11px; color: #4A5568; margin-top: 8px; line-height: 1.4; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; }";
    html += ".menu-button { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 120px; margin: 8px 4px; padding: 12px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:active { transform: translate(2px, 2px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.3), inset -1px -1px 0px rgba(255,255,255,0.1); }";
    html += ".menu-button.primary { background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%); border-color: #744210; text-shadow: 1px 1px 0px #744210; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #744210; }";
    html += ".menu-button.primary:hover { background: linear-gradient(145deg, #B7791F 0%, #975A16 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #744210; }";
    html += ".menu-button:disabled { background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); border-color: #4A5568; cursor: not-allowed; text-shadow: 1px 1px 0px #4A5568; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #4A5568; transform: none; }";
    html += ".button-group { text-align: center; margin-top: 25px; position: relative; z-index: 1; }";
    html += ".status-area { display: none; margin: 20px 0; padding: 20px; background: linear-gradient(145deg, #4A5568 0%, #2D3748 100%); border: 3px solid #1A202C; border-radius: 0; text-align: center; position: relative; z-index: 1; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.1), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A202C; }";
    html += ".status-connecting { color: #FBD38D; }";
    html += ".status-success { color: #68D391; }";
    html += ".status-error { color: #FC8181; }";
    html += ".status-icon { font-size: 20px; margin-bottom: 8px; font-family: 'Courier New', monospace; }";
    html += ".status-text { font-size: 14px; font-weight: bold; margin-bottom: 6px; color: #F7FAFC; text-shadow: 1px 1px 0px #1A202C; font-family: 'Courier New', monospace; }";
    html += ".status-detail { font-size: 12px; color: #E2E8F0; text-shadow: 1px 1px 0px #1A202C; font-family: 'Courier New', monospace; }";
    html += "form { max-width: 450px; margin: 0 auto; position: relative; z-index: 1; }";
    html += "@media (min-width: 768px) { .container { max-width: 650px; } .header h1 { font-size: 28px; } .menu-button { font-size: 15px; padding: 14px 24px; } }";
    html += "@media (max-width: 360px) { body { padding: 15px; } .header { padding: 20px 15px; } .header h1 { font-size: 22px; } .content { padding: 20px 15px; } }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>时钟设置</h1>";
    html += "</div>";
    html += "<div class='content'>";
    html += "<form id='clockForm' action='/timesync' method='POST' onsubmit='return handleTimeSync(event)'>";
    html += "<div class='form-group'>";
    html += "<label for='timezone'>时区设置:</label>";
    html += "<select id='timezone' name='timezone' onchange='updateTimezoneInput()'>";
    html += "<option value='UTC+8'" + String(savedTimezone == "UTC+8" ? " selected" : "") + ">中国标准时间 (UTC+8)</option>";
    html += "<option value='UTC+9'" + String(savedTimezone == "UTC+9" ? " selected" : "") + ">日本标准时间 (UTC+9)</option>";
    html += "<option value='UTC+0'" + String(savedTimezone == "UTC+0" ? " selected" : "") + ">格林威治时间 (UTC+0)</option>";
    html += "<option value='UTC-5'" + String(savedTimezone == "UTC-5" ? " selected" : "") + ">美国东部时间 (UTC-5)</option>";
    html += "<option value='UTC-8'" + String(savedTimezone == "UTC-8" ? " selected" : "") + ">美国西部时间 (UTC-8)</option>";

    // 检查是否是自定义时区
    bool isCustomTimezone = (savedTimezone != "UTC+8" && savedTimezone != "UTC+9" &&
                            savedTimezone != "UTC+0" && savedTimezone != "UTC-5" && savedTimezone != "UTC-8");
    html += "<option value='custom'" + String(isCustomTimezone ? " selected" : "") + ">自定义</option>";
    html += "</select>";
    html += "<input type='text' id='customTimezone' name='customTimezone' style='" +
             String(isCustomTimezone ? "display:block" : "display:none") + "; margin-top:8px;' " +
             "placeholder='输入自定义时区' value='" + String(isCustomTimezone ? savedTimezone : "") + "'>";
    html += "<div class='example-text'>示例: UTC+8, UTC-5, UTC+0</div>";
    html += "</div>";
    html += "<div class='form-group'>";
    html += "<label for='ntpServer'>NTP服务器:</label>";
    html += "<select id='ntpServer' name='ntpServer' onchange='updateNtpInput()'>";
    html += "<option value='ntp.aliyun.com'" + String(savedNtpServer == "ntp.aliyun.com" ? " selected" : "") + ">阿里云NTP (ntp.aliyun.com)</option>";
    html += "<option value='time.pool.aliyun.com'" + String(savedNtpServer == "time.pool.aliyun.com" ? " selected" : "") + ">阿里云时间池 (time.pool.aliyun.com)</option>";
    html += "<option value='cn.pool.ntp.org'" + String(savedNtpServer == "cn.pool.ntp.org" ? " selected" : "") + ">中国NTP池 (cn.pool.ntp.org)</option>";
    html += "<option value='pool.ntp.org'" + String(savedNtpServer == "pool.ntp.org" ? " selected" : "") + ">全球NTP池 (pool.ntp.org)</option>";
    html += "<option value='time.nist.gov'" + String(savedNtpServer == "time.nist.gov" ? " selected" : "") + ">美国NIST (time.nist.gov)</option>";

    // 检查是否是自定义NTP服务器
    bool isCustomNtp = (savedNtpServer != "ntp.aliyun.com" && savedNtpServer != "time.pool.aliyun.com" &&
                       savedNtpServer != "cn.pool.ntp.org" && savedNtpServer != "pool.ntp.org" && savedNtpServer != "time.nist.gov");
    html += "<option value='custom'" + String(isCustomNtp ? " selected" : "") + ">自定义</option>";
    html += "</select>";
    html += "<input type='text' id='customNtp' name='customNtp' style='" +
             String(isCustomNtp ? "display:block" : "display:none") + "; margin-top:8px;' " +
             "placeholder='输入自定义NTP服务器' value='" + String(isCustomNtp ? savedNtpServer : "") + "'>";
    html += "<div class='example-text'>示例: pool.ntp.org, time.google.com</div>";
    html += "</div>";
    html += "<div id='statusArea' class='status-area'>";
    html += "<div id='statusIcon' class='status-icon'></div>";
    html += "<div id='statusText' class='status-text'></div>";
    html += "<div id='statusDetail' class='status-detail'></div>";
    html += "</div>";
    html += "<div class='button-group'>";
    html += "<button type='submit' id='submitBtn' class='menu-button primary'>确认</button>";
    html += "<a href='/' class='menu-button' id='returnBtn'>返回</a>";
    html += "</div>";
    html += "</form>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "function updateTimezoneInput() {";
    html += "  const select = document.getElementById('timezone');";
    html += "  const customInput = document.getElementById('customTimezone');";
    html += "  if (select.value === 'custom') {";
    html += "    customInput.style.display = 'block';";
    html += "    customInput.required = true;";
    html += "  } else {";
    html += "    customInput.style.display = 'none';";
    html += "    customInput.required = false;";
    html += "  }";
    html += "}";
    html += "function updateNtpInput() {";
    html += "  const select = document.getElementById('ntpServer');";
    html += "  const customInput = document.getElementById('customNtp');";
    html += "  if (select.value === 'custom') {";
    html += "    customInput.style.display = 'block';";
    html += "    customInput.required = true;";
    html += "  } else {";
    html += "    customInput.style.display = 'none';";
    html += "    customInput.required = false;";
    html += "  }";
    html += "}";
    html += "function handleTimeSync(event) {";
    html += "  event.preventDefault();";
    html += "  const timezone = document.getElementById('timezone').value === 'custom' ? document.getElementById('customTimezone').value : document.getElementById('timezone').value;";
    html += "  const ntpServer = document.getElementById('ntpServer').value === 'custom' ? document.getElementById('customNtp').value : document.getElementById('ntpServer').value;";
    html += "  if (!timezone || !ntpServer) { alert('请完整填写时区和NTP服务器'); return false; }";
    html += "  document.getElementById('submitBtn').disabled = true;";
    html += "  document.getElementById('submitBtn').textContent = '同步中...';";
    html += "  document.getElementById('statusArea').style.display = 'block';";
    html += "  showStatus('connecting', '正在同步时间', '请稍候...');";
    html += "  const formData = new FormData();";
    html += "  formData.append('timezone', timezone);";
    html += "  formData.append('ntpServer', ntpServer);";
    html += "  fetch('/timesync', { method: 'POST', body: formData })";
    html += "    .then(response => response.json())";
    html += "    .then(data => {";
    html += "      if (data.status === 'success') {";
    html += "        showStatus('success', '时间同步成功', '当前时间: ' + data.currentTime);";
    html += "        document.getElementById('returnBtn').textContent = '关闭页面';";
    html += "      } else {";
    html += "        showStatus('error', '时间同步失败', data.message || '请检查网络连接和NTP服务器');";
    html += "        resetForm();";
    html += "      }";
    html += "    })";
    html += "    .catch(error => {";
    html += "      showStatus('error', '同步失败', '请检查网络连接');";
    html += "      resetForm();";
    html += "    });";
    html += "  return false;";
    html += "}";
    html += "function showStatus(type, text, detail) {";
    html += "  const statusArea = document.getElementById('statusArea');";
    html += "  const statusIcon = document.getElementById('statusIcon');";
    html += "  const statusText = document.getElementById('statusText');";
    html += "  const statusDetail = document.getElementById('statusDetail');";
    html += "  statusArea.className = 'status-area status-' + type;";
    html += "  statusArea.style.display = 'block';";
    html += "  switch(type) {";
    html += "    case 'connecting': statusIcon.textContent = '🔄'; break;";
    html += "    case 'success': statusIcon.textContent = '✅'; break;";
    html += "    case 'error': statusIcon.textContent = '❌'; break;";
    html += "  }";
    html += "  statusText.textContent = text;";
    html += "  statusDetail.textContent = detail;";
    html += "}";
    html += "function resetForm() {";
    html += "  document.getElementById('submitBtn').disabled = false;";
    html += "  document.getElementById('submitBtn').textContent = '确认';";
    html += "}";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Served clock setting page");
}

void WifiManager::handleTimeSync() {
    // 获取表单数据
    String timezone = server.arg("timezone");
    String ntpServer = server.arg("ntpServer");

    // 验证输入
    if (timezone.length() == 0 || ntpServer.length() == 0) {
        server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"时区和NTP服务器不能为空\"}");
        return;
    }

    USBSerial.print("Setting timezone: ");
    USBSerial.println(timezone);
    USBSerial.print("Setting NTP server: ");
    USBSerial.println(ntpServer);

    // 保存设置到Flash
    saveClockSettings(timezone, ntpServer);

    // 尝试同步时间
    bool syncSuccess = false;
    String currentTimeStr = "";

    // 检查WiFi连接状态
    if (WiFi.status() != WL_CONNECTED) {
        server.send(200, "application/json", "{\"status\":\"error\",\"message\":\"WiFi未连接，无法同步时间\"}");
        return;
    }

    // 配置NTP
    configTime(0, 0, ntpServer.c_str());

    // 等待时间同步
    int attempts = 0;
    time_t now = 0;
    while (now < 1000000000 && attempts < 20) { // 等待有效时间戳
        delay(500);
        time(&now);
        attempts++;
        USBSerial.print(".");
    }

    if (now > 1000000000) {
        // 时间同步成功，计算时区偏移
        int timezoneOffset = 0;
        if (timezone.startsWith("UTC+")) {
            timezoneOffset = timezone.substring(4).toInt() * 3600;
        } else if (timezone.startsWith("UTC-")) {
            timezoneOffset = -timezone.substring(4).toInt() * 3600;
        }

        USBSerial.print("Timezone offset: ");
        USBSerial.print(timezoneOffset);
        USBSerial.println(" seconds");

        // 重新配置时间，直接设置时区偏移
        configTime(timezoneOffset, 0, ntpServer.c_str());

        // 等待时区应用
        delay(1000);

        // 获取本地时间
        struct tm timeinfo;
        if (getLocalTime(&timeinfo)) {
            extern int currentHour;
            extern int currentMinute;
            currentHour = timeinfo.tm_hour;
            currentMinute = timeinfo.tm_min;

            char timeStr[20];
            sprintf(timeStr, "%02d:%02d", currentHour, currentMinute);
            currentTimeStr = String(timeStr);
            syncSuccess = true;

            USBSerial.println();
            USBSerial.print("Time synchronized successfully: ");
            USBSerial.print(currentTimeStr);
            USBSerial.print(" (UTC");
            if (timezoneOffset >= 0) {
                USBSerial.print("+");
                USBSerial.print(timezoneOffset / 3600);
            } else {
                USBSerial.print(timezoneOffset / 3600);
            }
            USBSerial.println(")");
        } else {
            USBSerial.println("Failed to get local time");
        }
    }

    // 返回结果
    if (syncSuccess) {
        String json = "{\"status\":\"success\",\"currentTime\":\"" + currentTimeStr + "\",\"message\":\"时间同步成功\"}";
        server.send(200, "application/json", json);
        USBSerial.println("Time sync completed successfully");
    } else {
        server.send(200, "application/json", "{\"status\":\"error\",\"message\":\"NTP服务器无响应或网络连接问题\"}");
        USBSerial.println("Time sync failed");
    }
}

void WifiManager::handleWifiConfig() {
    // 创建WiFi配置页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<title>配置WIFI - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 500px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #B91C1C; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; position: relative; z-index: 1; text-shadow: 2px 2px 0px #8B0000; letter-spacing: 1px; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; position: relative; }";
    html += ".form-group { margin-bottom: 20px; text-align: left; position: relative; z-index: 1; }";
    html += ".form-group label { display: block; margin-bottom: 8px; font-weight: bold; color: #2D3748; font-size: 14px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group input, .form-group select { width: 100%; padding: 12px; border: 3px solid #4A5568; border-radius: 0; font-size: 14px; font-family: 'Courier New', monospace; font-weight: bold; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); color: #2D3748; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group input:focus, .form-group select:focus { border-color: #D69E2E; outline: none; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8), 0 0 0 2px rgba(214, 158, 46, 0.3); }";
    html += ".scan-group { text-align: center; margin-bottom: 20px; position: relative; z-index: 1; }";
    html += ".wifi-list { margin: 20px 0; text-align: left; position: relative; z-index: 1; }";
    html += ".wifi-item { padding: 12px; border: 3px solid #4A5568; border-radius: 0; margin-bottom: 8px; cursor: pointer; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); transition: all 0.1s ease; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.2), 1px 1px 0px #2D3748; }";
    html += ".wifi-item:hover { background: linear-gradient(145deg, #FFF5B7 0%, #FED7AA 100%); border-color: #D69E2E; transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.2); }";
    html += ".wifi-item.selected { background: linear-gradient(145deg, #FFF5B7 0%, #FED7AA 100%); border-color: #D69E2E; transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8); }";
    html += ".wifi-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px; }";
    html += ".wifi-ssid { font-weight: bold; color: #2D3748; font-size: 14px; font-family: 'Courier New', monospace; }";
    html += ".wifi-signal { color: #38A169; font-weight: bold; font-size: 12px; font-family: 'Courier New', monospace; }";
    html += ".wifi-mac { font-size: 10px; color: #718096; font-family: 'Courier New', monospace; }";
    html += ".password-container { position: relative; }";
    html += ".password-toggle { position: absolute; right: 12px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #4A5568; font-size: 12px; font-weight: bold; background: rgba(255,255,255,0.8); padding: 2px 4px; border-radius: 2px; }";
    html += ".password-toggle:hover { background: rgba(214, 158, 46, 0.2); color: #D69E2E; }";
    html += ".menu-button { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 120px; margin: 8px 4px; padding: 12px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:active { transform: translate(2px, 2px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.3), inset -1px -1px 0px rgba(255,255,255,0.1); }";
    html += ".menu-button.primary { background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%); border-color: #744210; text-shadow: 1px 1px 0px #744210; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #744210; }";
    html += ".menu-button.primary:hover { background: linear-gradient(145deg, #B7791F 0%, #975A16 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #744210; }";
    html += ".menu-button.scan { background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); border-color: #1A365D; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button.scan:hover { background: linear-gradient(145deg, #2F855A 0%, #276749 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:disabled { background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); border-color: #4A5568; cursor: not-allowed; text-shadow: 1px 1px 0px #4A5568; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #4A5568; transform: none; }";
    html += ".button-group { text-align: center; margin-top: 20px; position: relative; z-index: 1; }";
    html += ".status-area { display: none; margin: 20px 0; padding: 20px; background: linear-gradient(145deg, #4A5568 0%, #2D3748 100%); border: 3px solid #1A202C; border-radius: 0; text-align: center; position: relative; z-index: 1; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.1), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A202C; }";
    html += ".status-connecting { color: #FBD38D; }";
    html += ".status-success { color: #68D391; }";
    html += ".status-error { color: #FC8181; }";
    html += ".status-icon { font-size: 20px; margin-bottom: 8px; font-family: 'Courier New', monospace; }";
    html += ".status-text { font-size: 14px; font-weight: bold; margin-bottom: 6px; color: #F7FAFC; text-shadow: 1px 1px 0px #1A202C; font-family: 'Courier New', monospace; }";
    html += ".status-detail { font-size: 12px; color: #E2E8F0; text-shadow: 1px 1px 0px #1A202C; font-family: 'Courier New', monospace; }";
    html += "form { max-width: 400px; margin: 0 auto; position: relative; z-index: 1; }";
    html += "#wifiList { display: none; }";
    html += "@media (min-width: 768px) { .container { max-width: 600px; } .header h1 { font-size: 28px; } .menu-button { font-size: 15px; padding: 14px 24px; } }";
    html += "@media (max-width: 360px) { body { padding: 15px; } .header { padding: 20px 15px; } .header h1 { font-size: 22px; } .content { padding: 20px 15px; } }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>配置WIFI</h1>";
    html += "</div>";
    html += "<div class='content'>";
    html += "<form id='wifiForm' action='/wificonnect' method='POST' onsubmit='return handleWifiConnect(event)'>";
    html += "<div class='scan-group'>";
    html += "<button type='button' class='menu-button scan' onclick='scanWifi()'>扫描WIFI</button>";
    html += "</div>";
    html += "<div id='wifiList' class='wifi-list'></div>";
    html += "<div class='form-group'>";
    html += "<label for='ssid'>SSID:</label>";
    html += "<input type='text' id='ssid' name='ssid' required>";
    html += "</div>";
    html += "<div class='form-group'>";
    html += "<label for='password'>密码:</label>";
    html += "<div class='password-container'>";
    html += "<input type='password' id='password' name='password' required>";
    html += "<span class='password-toggle' onclick='togglePassword()'>显示</span>";
    html += "</div>";
    html += "</div>";
    html += "<div id='statusArea' class='status-area'>";
    html += "<div id='statusIcon' class='status-icon'></div>";
    html += "<div id='statusText' class='status-text'></div>";
    html += "<div id='statusDetail' class='status-detail'></div>";
    html += "</div>";
    html += "<div class='button-group'>";
    html += "<button type='submit' id='submitBtn' class='menu-button primary'>确认</button>";
    html += "<a href='/' class='menu-button' id='returnBtn'>返回</a>";
    html += "</div>";
    html += "</form>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "let statusCheckInterval;";
    html += "function scanWifi() {";
    html += "  document.getElementById('wifiList').innerHTML = '<p>正在扫描...</p>';";
    html += "  document.getElementById('wifiList').style.display = 'block';";
    html += "  performWifiScan();";
    html += "}";
    html += "function performWifiScan() {";
    html += "  fetch('/wifiscan')";
    html += "    .then(response => response.json())";
    html += "    .then(data => {";
    html += "      if (data.status === 'scanning') {";
    html += "        document.getElementById('wifiList').innerHTML = '<p>' + data.message + '</p>';";
    html += "        setTimeout(performWifiScan, 1000);";  // 1秒后重试
    html += "      } else if (data.status === 'completed' && data.networks) {";
    html += "        let html = '';";
    html += "        data.networks.forEach(network => {";
    html += "          html += '<div class=\"wifi-item\" onclick=\"selectWifi(\\'' + network.ssid + '\\')\">';";
    html += "          html += '<div class=\"wifi-header\">';";
    html += "          html += '<div class=\"wifi-ssid\">' + network.ssid + '</div>';";
    html += "          html += '<div class=\"wifi-signal\">' + network.rssi + 'dBm</div>';";
    html += "          html += '</div>';";
    html += "          if (network.mac) html += '<div class=\"wifi-mac\">MAC: ' + network.mac + '</div>';";
    html += "          html += '</div>';";
    html += "        });";
    html += "        document.getElementById('wifiList').innerHTML = html || '<p>未发现WiFi网络</p>';";
    html += "      } else if (data.status === 'error') {";
    html += "        document.getElementById('wifiList').innerHTML = '<p>' + data.message + '</p>';";
    html += "      } else {";
    html += "        document.getElementById('wifiList').innerHTML = '<p>扫描失败，请重试</p>';";
    html += "      }";
    html += "    })";
    html += "    .catch(error => {";
    html += "      document.getElementById('wifiList').innerHTML = '<p>扫描失败，请重试</p>';";
    html += "    });";
    html += "}";
    html += "function selectWifi(ssid) {";
    html += "  document.getElementById('ssid').value = ssid;";
    html += "  document.querySelectorAll('.wifi-item').forEach(item => item.classList.remove('selected'));";
    html += "  event.target.closest('.wifi-item').classList.add('selected');";
    html += "}";
    html += "function togglePassword() {";
    html += "  const passwordField = document.getElementById('password');";
    html += "  const toggleButton = document.querySelector('.password-toggle');";
    html += "  if (passwordField.type === 'password') {";
    html += "    passwordField.type = 'text';";
    html += "    toggleButton.textContent = '隐藏';";
    html += "  } else {";
    html += "    passwordField.type = 'password';";
    html += "    toggleButton.textContent = '显示';";
    html += "  }";
    html += "}";
    html += "function handleWifiConnect(event) {";
    html += "  event.preventDefault();";
    html += "  const ssid = document.getElementById('ssid').value;";
    html += "  const password = document.getElementById('password').value;";
    html += "  if (!ssid) { alert('请输入SSID'); return false; }";
    html += "  document.getElementById('submitBtn').disabled = true;";
    html += "  document.getElementById('submitBtn').textContent = '连接中...';";
    html += "  document.getElementById('statusArea').style.display = 'block';";
    html += "  showStatus('connecting', '正在连接到 ' + ssid, '请稍候...');";
    html += "  const formData = new FormData();";
    html += "  formData.append('ssid', ssid);";
    html += "  formData.append('password', password);";
    html += "  fetch('/wificonnect', { method: 'POST', body: formData })";
    html += "    .then(response => response.text())";
    html += "    .then(data => {";
    html += "      showStatus('connecting', '正在测试连接到 ' + ssid, '成功后设备将自动重启...');";
    html += "      setTimeout(() => {";
    html += "        showStatus('info', '连接测试中', '如果连接成功，设备会自动重启。如果失败，请检查密码后重试。');";
    html += "      }, 3000);";
    html += "    })";
    html += "    .catch(error => {";
    html += "      showStatus('error', '提交失败', '请检查网络连接');";
    html += "      resetForm();";
    html += "    });";
    html += "  return false;";
    html += "}";
    html += "function showStatus(type, text, detail) {";
    html += "  const statusArea = document.getElementById('statusArea');";
    html += "  const statusIcon = document.getElementById('statusIcon');";
    html += "  const statusText = document.getElementById('statusText');";
    html += "  const statusDetail = document.getElementById('statusDetail');";
    html += "  statusArea.className = 'status-area status-' + type;";
    html += "  statusArea.style.display = 'block';";
    html += "  switch(type) {";
    html += "    case 'connecting': statusIcon.textContent = '🔄'; break;";
    html += "    case 'success': statusIcon.textContent = '✅'; break;";
    html += "    case 'error': statusIcon.textContent = '❌'; break;";
    html += "  }";
    html += "  statusText.textContent = text;";
    html += "  statusDetail.textContent = detail;";
    html += "}";
    html += "function resetForm() {";
    html += "  document.getElementById('submitBtn').disabled = false;";
    html += "  document.getElementById('submitBtn').textContent = '确认';";
    html += "}";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Served WiFi config page");
}

void WifiManager::handleWifiScan() {
    USBSerial.println("WiFi scan request received");

    // 检查当前扫描状态
    switch (scanState) {
        case SCAN_IDLE:
        {
            // 在开始扫描前，确保WiFi模块处于正确状态
            USBSerial.println("Preparing WiFi for scan...");
            USBSerial.print("Current WiFi status: ");
            USBSerial.println(WiFi.status());
            USBSerial.print("Current WiFi mode: ");
            USBSerial.println(WiFi.getMode());

            // 清理任何正在进行的扫描
            WiFi.scanDelete();
            delay(50);

            // 使用统一的重置函数
            resetWiFiForScan();

            USBSerial.print("WiFi mode after reset: ");
            USBSerial.println(WiFi.getMode());
            USBSerial.print("WiFi status after reset: ");
            USBSerial.println(WiFi.status());

            // 开始异步WiFi扫描
            USBSerial.println("Starting async WiFi scan...");
            int16_t scanResult = WiFi.scanNetworks(true);  // true = 异步扫描
            USBSerial.print("scanNetworks() returned: ");
            USBSerial.println(scanResult);

            if (scanResult == WIFI_SCAN_RUNNING) {
                scanState = SCAN_IN_PROGRESS;
                scanStartTime = millis();
                server.send(200, "application/json", "{\"status\":\"scanning\",\"message\":\"正在扫描...\"}");
                USBSerial.println("Async WiFi scan started successfully");
            } else {
                scanState = SCAN_FAILED;
                server.send(500, "application/json", "{\"status\":\"error\",\"message\":\"扫描启动失败\"}");
                USBSerial.print("Failed to start WiFi scan, result code: ");
                USBSerial.println(scanResult);
            }
            break;
        }

        case SCAN_IN_PROGRESS:
            // 扫描正在进行中
            server.send(200, "application/json", "{\"status\":\"scanning\",\"message\":\"正在扫描...\"}");
            USBSerial.println("WiFi scan still in progress");
            break;

        case SCAN_COMPLETED:
            // 扫描已完成，返回缓存的结果
            server.send(200, "application/json", cachedScanResult);
            USBSerial.println("Returned cached WiFi scan results");
            // 重置状态，允许下次扫描
            scanState = SCAN_IDLE;
            break;

        case SCAN_FAILED:
            // 扫描失败，尝试重置WiFi状态
            USBSerial.println("WiFi scan failed, attempting to reset WiFi state");
            resetWiFiForScan();
            server.send(500, "application/json", "{\"status\":\"error\",\"message\":\"扫描失败，已重置WiFi状态，请重试\"}");
            // 重置状态，允许重试
            scanState = SCAN_IDLE;
            break;
    }
}

void WifiManager::handleWifiConnect() {
    // 获取表单数据
    String ssid = server.arg("ssid");
    String password = server.arg("password");

    // 验证输入
    if (ssid.length() == 0) {
        server.send(400, "text/plain", "SSID不能为空");
        return;
    }

    USBSerial.print("User attempting to connect to WiFi: ");
    USBSerial.println(ssid);

    // 保存WiFi凭据到Flash
    saveWifiCredentials(ssid, password);

    // 先返回响应给用户
    server.send(200, "text/plain", "正在测试连接，成功后将自动重启...");
    delay(100); // 确保响应发送完成

    // 进行同步WiFi连接测试
    USBSerial.println("Testing WiFi connection...");

    // 断开当前连接
    WiFi.disconnect();
    delay(100);

    // 确保WiFi模式正确设置为AP+STA
    WiFi.mode(WIFI_AP_STA);
    delay(100);

    // 开始连接测试
    WiFi.begin(ssid.c_str(), password.c_str());

    // 同步等待连接结果（最多10秒）
    int attempts = 0;
    const int maxAttempts = 20; // 10秒，每次等待500ms

    while (WiFi.status() != WL_CONNECTED && attempts < maxAttempts) {
        delay(500);
        attempts++;
        USBSerial.print(".");

        // 检查是否明确失败
        wl_status_t status = WiFi.status();
        if (isQuickWiFiFailure(status)) {
            USBSerial.println();
            USBSerial.println("WiFi connection test failed");
            return; // 连接失败，不重启
        }
    }

    if (WiFi.status() == WL_CONNECTED) {
        USBSerial.println();
        USBSerial.println("WiFi connection test successful! Restarting device...");
        delay(1000);
        ESP.restart(); // 连接成功，重启设备进行完整初始化
    } else {
        USBSerial.println();
        USBSerial.println("WiFi connection test timed out");
        // 连接失败，不重启，保持当前状态
    }
}

void WifiManager::handleWifiStatus() {
    // 返回固定的系统状态，不再进行动态查询
    String json = "{";

    if (finalAutoMode) {
        // 自动模式：显示已连接状态
        json += "\"ssid\":\"" + finalWifiSSID + "\",";
        json += "\"status\":\"connected\",";
        json += "\"ip\":\"" + finalWifiIP + "\",";
        json += "\"clockMode\":\"自动模式\",";
        json += "\"message\":\"已连接\"";
    } else {
        // 离线模式：显示离线状态
        json += "\"ssid\":\"\",";
        json += "\"status\":\"offline\",";
        json += "\"clockMode\":\"离线模式\",";
        json += "\"message\":\"离线模式\"";
    }

    json += "}";

    server.send(200, "application/json", json);

    USBSerial.print("WiFi status query (static): ");
    USBSerial.println(json);
}



void WifiManager::updateWifiScanState() {
    if (scanState == SCAN_IN_PROGRESS) {
        unsigned long currentTime = millis();

        // 检查扫描超时（15秒）
        if (currentTime - scanStartTime > 15000) {
            scanState = SCAN_FAILED;
            USBSerial.println("WiFi scan timeout, resetting WiFi state");
            resetWiFiForScan();
            return;
        }

        // 检查扫描是否完成
        int16_t scanResult = WiFi.scanComplete();

        if (scanResult == WIFI_SCAN_RUNNING) {
            // 扫描仍在进行中，继续等待
            return;
        } else if (scanResult == WIFI_SCAN_FAILED) {
            // 扫描失败
            scanState = SCAN_FAILED;
            USBSerial.println("WiFi scan failed, resetting WiFi state");
            resetWiFiForScan();
            return;
        } else if (scanResult >= 0) {
            // 扫描完成，处理结果
            int networkCount = scanResult;
            USBSerial.print("WiFi scan completed, found ");
            USBSerial.print(networkCount);
            USBSerial.println(" networks");

            // 创建JSON响应并缓存
            String json = "{\"status\":\"completed\",\"networks\":[";

            if (networkCount > 0) {
                for (int i = 0; i < networkCount; i++) {
                    if (i > 0) json += ",";

                    String ssid = WiFi.SSID(i);
                    String mac = WiFi.BSSIDstr(i);
                    int32_t rssi = WiFi.RSSI(i);

                    // 计算信号强度显示
                    String signalStr;
                    if (rssi > -50) signalStr = "优秀";
                    else if (rssi > -60) signalStr = "良好";
                    else if (rssi > -70) signalStr = "一般";
                    else signalStr = "较弱";

                    json += "{";
                    json += "\"ssid\":\"" + ssid + "\",";
                    json += "\"mac\":\"" + mac + "\",";
                    json += "\"signal\":\"" + signalStr + " (" + String(rssi) + "dBm)\",";
                    json += "\"rssi\":" + String(rssi);
                    json += "}";
                }
            }

            json += "]}";

            // 缓存结果并标记完成
            cachedScanResult = json;
            scanState = SCAN_COMPLETED;

            // 清理扫描结果以释放内存
            WiFi.scanDelete();
        }
    }
}

void WifiManager::saveWifiCredentials(String ssid, String password) {
    preferences.begin("wifi", false);
    preferences.putString("ssid", ssid);
    preferences.putString("password", password);
    preferences.end();

    USBSerial.println("WiFi credentials saved to Flash");
    USBSerial.print("SSID: ");
    USBSerial.println(ssid);
}

void WifiManager::loadWifiCredentials() {
    preferences.begin("wifi", true);
    String savedSSID = preferences.getString("ssid", "");
    String savedPassword = preferences.getString("password", "");
    preferences.end();

    if (savedSSID.length() > 0) {
        USBSerial.println("Found saved WiFi credentials, attempting to connect...");
        USBSerial.print("SSID: ");
        USBSerial.println(savedSSID);

        // 启动异步连接到保存的WiFi
        WiFi.begin(savedSSID.c_str(), savedPassword.c_str());

        // 设置连接状态为正在连接
        currentWifiState = WIFI_CONNECTING;
        connectingSSID = savedSSID;
        wifiConnectStartTime = millis();

        USBSerial.println("WiFi connection initiated (non-blocking)");
    } else {
        USBSerial.println("No saved WiFi credentials found");
    }
}

void WifiManager::saveClockSettings(String timezone, String ntpServer) {
    preferences.begin("clock", false);
    preferences.putString("timezone", timezone);
    preferences.putString("ntpServer", ntpServer);
    preferences.end();

    // 更新内存中的设置
    savedTimezone = timezone;
    savedNtpServer = ntpServer;

    USBSerial.println("Clock settings saved to Flash");
    USBSerial.print("Timezone: ");
    USBSerial.println(timezone);
    USBSerial.print("NTP Server: ");
    USBSerial.println(ntpServer);

    // 通知NTP自动同步模块配置已更新
    if (ntpAutoSyncCallback != nullptr) {
        ntpAutoSyncCallback(ntpServer, timezone);
    }
}

void WifiManager::loadClockSettings() {
    preferences.begin("clock", true);
    savedTimezone = preferences.getString("timezone", "UTC+8");
    savedNtpServer = preferences.getString("ntpServer", "ntp.aliyun.com");
    preferences.end();

    USBSerial.println("Clock settings loaded from Flash");
    USBSerial.print("Timezone: ");
    USBSerial.println(savedTimezone);
    USBSerial.print("NTP Server: ");
    USBSerial.println(savedNtpServer);
}

void WifiManager::saveOfflineMode(bool enabled) {
    preferences.begin("clock", false);
    preferences.putBool("offlineMode", enabled);
    preferences.end();

    // 更新内存中的设置
    offlineModeEnabled = enabled;

    USBSerial.println("Offline mode setting saved to Flash");
    USBSerial.print("Offline Mode Enabled: ");
    USBSerial.println(enabled ? "true" : "false");
}

void WifiManager::loadOfflineMode() {
    preferences.begin("clock", true);
    offlineModeEnabled = preferences.getBool("offlineMode", false);
    preferences.end();

    USBSerial.println("Offline mode setting loaded from Flash");
    USBSerial.print("Offline Mode Enabled: ");
    USBSerial.println(offlineModeEnabled ? "true" : "false");
}

void WifiManager::setNTPAutoSyncCallback(void (*callback)(const String&, const String&)) {
    ntpAutoSyncCallback = callback;
}

void WifiManager::setModeChangeCallback(void (*callback)()) {
    modeChangeCallback = callback;
}

void WifiManager::syncTimeFromNTP() {
    // 检查WiFi连接状态
    if (WiFi.status() != WL_CONNECTED) {
        USBSerial.println("WiFi not connected, skipping NTP sync");
        return;
    }

    if (savedNtpServer.length() == 0) {
        USBSerial.println("No NTP server configured, skipping sync");
        return;
    }

    USBSerial.print("Syncing time from NTP server: ");
    USBSerial.println(savedNtpServer);
    USBSerial.print("Using timezone: ");
    USBSerial.println(savedTimezone);

    // 计算时区偏移
    int timezoneOffset = 0;
    if (savedTimezone.startsWith("UTC+")) {
        timezoneOffset = savedTimezone.substring(4).toInt() * 3600;
    } else if (savedTimezone.startsWith("UTC-")) {
        timezoneOffset = -savedTimezone.substring(4).toInt() * 3600;
    }

    USBSerial.print("Timezone offset: ");
    USBSerial.print(timezoneOffset);
    USBSerial.println(" seconds");

    // 配置NTP，直接设置时区偏移
    configTime(timezoneOffset, 0, savedNtpServer.c_str());

    // 等待时间同步
    int attempts = 0;
    time_t now = 0;
    while (now < 1000000000 && attempts < 20) {
        delay(500);
        time(&now);
        attempts++;
        USBSerial.print(".");
    }

    if (now > 1000000000) {
        // 等待时区应用
        delay(1000);

        // 获取当前时间并设置到马里奥时钟
        struct tm timeinfo;
        if (getLocalTime(&timeinfo)) {
            extern int currentHour;
            extern int currentMinute;
            currentHour = timeinfo.tm_hour;
            currentMinute = timeinfo.tm_min;

            USBSerial.println();
            USBSerial.print("Time synchronized successfully: ");
            USBSerial.print(currentHour);
            USBSerial.print(":");
            USBSerial.print(currentMinute);
            USBSerial.print(" (UTC");
            if (timezoneOffset >= 0) {
                USBSerial.print("+");
                USBSerial.print(timezoneOffset / 3600);
            } else {
                USBSerial.print(timezoneOffset / 3600);
            }
            USBSerial.println(")");
        } else {
            USBSerial.println("Failed to get local time");
        }
    } else {
        USBSerial.println();
        USBSerial.println("NTP sync failed");
    }
}

void WifiManager::handleDeviceConfig() {
    // 创建设备配置页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<title>设备配置 - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 550px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #B91C1C; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; position: relative; z-index: 1; text-shadow: 2px 2px 0px #8B0000; letter-spacing: 1px; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; position: relative; }";
    html += ".form-group { margin-bottom: 25px; text-align: left; position: relative; z-index: 1; }";
    html += ".form-group label { display: block; margin-bottom: 12px; font-weight: bold; color: #2D3748; font-size: 16px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group input[type='number'], .form-group select { width: 100%; padding: 12px; border: 3px solid #4A5568; border-radius: 0; font-size: 14px; font-family: 'Courier New', monospace; font-weight: bold; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); color: #2D3748; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8); }";
    html += ".form-group input[type='number']:focus, .form-group select:focus { border-color: #D69E2E; outline: none; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8), 0 0 0 2px rgba(214, 158, 46, 0.3); }";
    html += ".example-text { font-size: 11px; color: #4A5568; margin-top: 8px; line-height: 1.4; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; }";
    html += ".input-with-button { display: flex; align-items: center; gap: 8px; }";
    html += ".input-with-button input { flex: 1; }";
    html += ".menu-button { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 80px; margin: 0; padding: 8px 16px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 12px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:active { transform: translate(2px, 2px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.3), inset -1px -1px 0px rgba(255,255,255,0.1); }";
    html += ".menu-button.primary { background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%); border-color: #744210; text-shadow: 1px 1px 0px #744210; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #744210; }";
    html += ".menu-button.primary:hover { background: linear-gradient(145deg, #B7791F 0%, #975A16 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #744210; }";
    html += ".menu-button.apply { background: linear-gradient(145deg, #FF9800 0%, #F57C00 100%); border-color: #E65100; text-shadow: 1px 1px 0px #E65100; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #E65100; }";
    html += ".menu-button.apply:hover { background: linear-gradient(145deg, #F57C00 0%, #EF6C00 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #E65100; }";
    html += ".menu-button:disabled { background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); border-color: #4A5568; cursor: not-allowed; text-shadow: 1px 1px 0px #4A5568; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #4A5568; transform: none; }";
    html += ".button-group { text-align: center; margin-top: 25px; position: relative; z-index: 1; }";
    html += ".button-group .menu-button { min-width: 120px; margin: 8px 4px; padding: 12px 20px; font-size: 14px; }";
    html += ".status-area { display: none; margin: 20px 0; padding: 20px; background: linear-gradient(145deg, #4A5568 0%, #2D3748 100%); border: 3px solid #1A202C; border-radius: 0; text-align: center; position: relative; z-index: 1; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.1), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A202C; }";
    html += ".status-success { color: #68D391; }";
    html += ".status-error { color: #FC8181; }";
    html += ".status-icon { font-size: 20px; margin-bottom: 8px; font-family: 'Courier New', monospace; }";
    html += ".status-text { font-size: 14px; font-weight: bold; margin-bottom: 6px; color: #F7FAFC; text-shadow: 1px 1px 0px #1A202C; font-family: 'Courier New', monospace; }";
    html += ".status-detail { font-size: 12px; color: #E2E8F0; text-shadow: 1px 1px 0px #1A202C; font-family: 'Courier New', monospace; }";
    html += "form { max-width: 450px; margin: 0 auto; position: relative; z-index: 1; }";
    html += ".disabled-section { opacity: 0.6; position: relative; }";
    html += ".disabled-section::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(160, 174, 192, 0.3) 10px, rgba(160, 174, 192, 0.3) 20px); pointer-events: none; z-index: 2; }";
    html += ".disabled-note { font-size: 11px; color: #718096; font-style: italic; margin-top: 8px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; background: rgba(160, 174, 192, 0.1); padding: 6px; border: 2px solid #A0AEC0; }";
    html += "@media (min-width: 768px) { .container { max-width: 650px; } .header h1 { font-size: 28px; } .button-group .menu-button { font-size: 15px; padding: 14px 24px; } }";
    html += "@media (max-width: 360px) { body { padding: 15px; } .header { padding: 20px 15px; } .header h1 { font-size: 22px; } .content { padding: 20px 15px; } }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>设备配置</h1>";
    html += "</div>";
    html += "<div class='content'>";
    html += "<form id='deviceForm' action='/devicesettings' method='POST' onsubmit='return handleDeviceSettings(event)'>";
    html += "<div class='form-group'>";
    html += "<label for='brightness'>屏幕亮度:</label>";
    html += "<div class='input-with-button'>";
    html += "<input type='number' id='brightness' name='brightness' min='0' max='255' value='" + String(savedBrightness) + "' required>";
    html += "<button type='button' class='menu-button apply' onclick='applyBrightness()'>应用</button>";
    html += "</div>";
    html += "<div class='example-text'>范围: 0-255 (0=最暗, 255=最亮)</div>";
    html += "</div>";
    html += "<div class='form-group disabled-section'>";
    html += "<label for='orientation'>屏幕方向:</label>";
    html += "<select id='orientation' name='orientation' disabled>";
    html += "<option value='usb_down'";
    if (savedOrientation == "usb_down") html += " selected";
    html += ">USB口向下</option>";
    html += "<option value='usb_right'";
    if (savedOrientation == "usb_right") html += " selected";
    html += ">USB口向右</option>";
    html += "<option value='usb_left'";
    if (savedOrientation == "usb_left") html += " selected";
    html += ">USB口向左</option>";
    html += "<option value='usb_up'";
    if (savedOrientation == "usb_up") html += " selected";
    html += ">USB口向上</option>";
    html += "</select>";
    html += "<div class='disabled-note'>此功能暂未实现，接口已预留</div>";
    html += "</div>";
    html += "<div id='statusArea' class='status-area'>";
    html += "<div id='statusIcon' class='status-icon'></div>";
    html += "<div id='statusText' class='status-text'></div>";
    html += "<div id='statusDetail' class='status-detail'></div>";
    html += "</div>";
    html += "<div class='button-group'>";
    html += "<button type='submit' id='submitBtn' class='menu-button primary'>确认</button>";
    html += "<a href='/' class='menu-button' id='returnBtn'>返回</a>";
    html += "</div>";
    html += "</form>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "function applyBrightness() {";
    html += "  const brightness = document.getElementById('brightness').value;";
    html += "  if (!brightness || brightness < 0 || brightness > 255) {";
    html += "    alert('请输入有效的亮度值 (0-255)');";
    html += "    return;";
    html += "  }";
    html += "  const formData = new FormData();";
    html += "  formData.append('brightness', brightness);";
    html += "  fetch('/brightnesstest', { method: 'POST', body: formData })";
    html += "    .then(response => response.json())";
    html += "    .then(data => {";
    html += "      if (data.status === 'success') {";
    html += "        showStatus('success', '亮度应用', '亮度已设置为 ' + brightness + ' (临时生效)');";
    html += "      } else {";
    html += "        showStatus('error', '应用失败', data.message || '无法设置亮度');";
    html += "      }";
    html += "    })";
    html += "    .catch(error => {";
    html += "      showStatus('error', '应用失败', '请检查网络连接');";
    html += "    });";
    html += "}";
    html += "function handleDeviceSettings(event) {";
    html += "  event.preventDefault();";
    html += "  const brightness = document.getElementById('brightness').value;";
    html += "  const orientation = document.getElementById('orientation').value;";
    html += "  if (!brightness || brightness < 0 || brightness > 255) {";
    html += "    alert('请输入有效的亮度值 (0-255)');";
    html += "    return false;";
    html += "  }";
    html += "  const submitBtn = document.getElementById('submitBtn');";
    html += "  submitBtn.disabled = true;";
    html += "  submitBtn.textContent = '保存中...';";
    html += "  submitBtn.className = 'menu-button primary';";
    html += "  document.getElementById('statusArea').style.display = 'block';";
    html += "  showStatus('success', '正在保存设置', '请稍候...');";
    html += "  const formData = new FormData();";
    html += "  formData.append('brightness', brightness);";
    html += "  formData.append('orientation', orientation);";
    html += "  fetch('/devicesettings', { method: 'POST', body: formData })";
    html += "    .then(response => response.json())";
    html += "    .then(data => {";
    html += "      if (data.status === 'success') {";
    html += "        showStatus('success', '设置保存成功', '亮度: ' + brightness + ', 方向: ' + getOrientationText(orientation));";
    html += "        const returnBtn = document.getElementById('returnBtn');";
    html += "        returnBtn.textContent = '关闭页面';";
    html += "        resetForm();";  // 添加重置表单状态的调用
    html += "      } else {";
    html += "        showStatus('error', '保存失败', data.message || '请重试');";
    html += "        resetForm();";
    html += "      }";
    html += "    })";
    html += "    .catch(error => {";
    html += "      showStatus('error', '保存失败', '请检查网络连接');";
    html += "      resetForm();";
    html += "    });";
    html += "  return false;";
    html += "}";
    html += "function getOrientationText(value) {";
    html += "  switch(value) {";
    html += "    case 'usb_down': return 'USB口向下';";
    html += "    case 'usb_right': return 'USB口向右';";
    html += "    case 'usb_left': return 'USB口向左';";
    html += "    case 'usb_up': return 'USB口向上';";
    html += "    default: return value;";
    html += "  }";
    html += "}";
    html += "function showStatus(type, text, detail) {";
    html += "  const statusArea = document.getElementById('statusArea');";
    html += "  const statusIcon = document.getElementById('statusIcon');";
    html += "  const statusText = document.getElementById('statusText');";
    html += "  const statusDetail = document.getElementById('statusDetail');";
    html += "  statusArea.className = 'status-area status-' + type;";
    html += "  statusArea.style.display = 'block';";
    html += "  switch(type) {";
    html += "    case 'success': statusIcon.textContent = '✅'; break;";
    html += "    case 'error': statusIcon.textContent = '❌'; break;";
    html += "  }";
    html += "  statusText.textContent = text;";
    html += "  statusDetail.textContent = detail;";
    html += "}";
    html += "function resetForm() {";
    html += "  const submitBtn = document.getElementById('submitBtn');";
    html += "  submitBtn.disabled = false;";
    html += "  submitBtn.textContent = '确认';";
    html += "  submitBtn.className = 'menu-button primary';";
    html += "}";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Served device config page");
}

void WifiManager::handleBrightnessTest() {
    // 获取亮度值
    String brightnessStr = server.arg("brightness");

    if (brightnessStr.length() == 0) {
        server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"亮度值不能为空\"}");
        return;
    }

    int brightness = brightnessStr.toInt();

    // 验证亮度范围
    if (brightness < 0 || brightness > 255) {
        server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"亮度值必须在0-255之间\"}");
        return;
    }

    USBSerial.print("Testing brightness: ");
    USBSerial.println(brightness);

    // 应用亮度设置（临时）
    applyBrightness(brightness);

    // 返回成功响应
    String json = "{\"status\":\"success\",\"brightness\":" + String(brightness) + ",\"message\":\"亮度测试成功\"}";
    server.send(200, "application/json", json);
    USBSerial.println("Brightness test completed");
}

void WifiManager::handleDeviceSettings() {
    // 获取表单数据
    String brightnessStr = server.arg("brightness");
    String orientation = server.arg("orientation");

    // 验证输入
    if (brightnessStr.length() == 0) {
        server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"亮度值不能为空\"}");
        return;
    }

    int brightness = brightnessStr.toInt();

    // 验证亮度范围
    if (brightness < 0 || brightness > 255) {
        server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"亮度值必须在0-255之间\"}");
        return;
    }

    USBSerial.print("Saving device settings - Brightness: ");
    USBSerial.print(brightness);
    USBSerial.print(", Orientation: ");
    USBSerial.println(orientation);

    // 保存设置到Flash
    saveDeviceSettings(brightness, orientation);

    // 应用亮度设置
    applyBrightness(brightness);

    // 返回成功响应
    String json = "{\"status\":\"success\",\"brightness\":" + String(brightness) + ",\"orientation\":\"" + orientation + "\",\"message\":\"设置保存成功\"}";
    server.send(200, "application/json", json);
    USBSerial.println("Device settings saved successfully");
}

void WifiManager::saveDeviceSettings(int brightness, String orientation) {
    preferences.begin("device", false);
    preferences.putInt("brightness", brightness);
    preferences.putString("orientation", orientation);
    preferences.end();

    // 更新内存中的设置
    savedBrightness = brightness;
    savedOrientation = orientation;

    USBSerial.println("Device settings saved to Flash");
    USBSerial.print("Brightness: ");
    USBSerial.println(brightness);
    USBSerial.print("Orientation: ");
    USBSerial.println(orientation);
}

void WifiManager::loadDeviceSettings() {
    preferences.begin("device", true);
    savedBrightness = preferences.getInt("brightness", 128);  // 默认亮度50%
    savedOrientation = preferences.getString("orientation", "usb_down");  // 默认USB口向下
    preferences.end();

    USBSerial.println("Device settings loaded from Flash");
    USBSerial.print("Brightness: ");
    USBSerial.println(savedBrightness);
    USBSerial.print("Orientation: ");
    USBSerial.println(savedOrientation);

    // 应用加载的设置
    applyBrightness(savedBrightness);
}

void WifiManager::applyBrightness(int brightness) {
    USBSerial.print("Applying brightness: ");
    USBSerial.println(brightness);

    // 调用外部的背光亮度控制函数
    extern void setBacklightBrightness(uint8_t brightness);
    setBacklightBrightness((uint8_t)brightness);

    USBSerial.print("Brightness successfully applied: ");
    USBSerial.println(brightness);
}

void WifiManager::handleClient() {
    // 处理DNS服务器请求（用于Captive Portal）
    dnsServer.processNextRequest();

    // 只处理我们自己的Web服务器请求，不再使用WiFiManager的process
    server.handleClient();

    // 更新WiFi扫描状态
    updateWifiScanState();
}

void WifiManager::handleStockConfig() {
    // 创建股票配置页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<title>股票设置 - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 550px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #B91C1C; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; position: relative; z-index: 1; text-shadow: 2px 2px 0px #8B0000; letter-spacing: 1px; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; position: relative; }";
    html += ".form-group { margin-bottom: 25px; text-align: left; position: relative; z-index: 1; }";
    html += ".form-group > label:first-child { display: block; margin-bottom: 12px; font-weight: bold; color: #2D3748; font-size: 16px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += ".switch { position: relative; display: inline-block; width: 70px; height: 40px; margin-top: 8px; }";
    html += ".switch input { opacity: 0; width: 0; height: 0; }";
    html += ".slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); transition: .3s; border: 3px solid #4A5568; border-radius: 0; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #2D3748; }";
    html += ".slider:before { position: absolute; content: ''; height: 28px; width: 28px; left: 4px; bottom: 4px; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); transition: .3s; border: 2px solid #4A5568; border-radius: 0; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.2); }";
    html += "input:checked + .slider { background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); border-color: #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += "input:checked + .slider:before { transform: translateX(26px); background: linear-gradient(145deg, #FFF5B7 0%, #FED7AA 100%); border-color: #D69E2E; }";
    html += ".description { color: #4A5568; font-size: 12px; margin-top: 8px; line-height: 1.4; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; }";
    html += ".description.warning { color: #D69E2E; font-weight: bold; background: rgba(214, 158, 46, 0.1); padding: 8px; border: 2px solid #D69E2E; margin-top: 12px; border-radius: 0; }";
    html += ".stock-inputs { margin-top: 15px; position: relative; z-index: 1; }";
    html += ".stock-input-row { display: flex; align-items: center; margin-bottom: 12px; gap: 10px; }";
    html += ".stock-input-row label { min-width: 70px; font-weight: bold; color: #2D3748; font-size: 14px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; margin-bottom: 0; }";
    html += ".stock-input-row input { flex: 1; padding: 10px; border: 3px solid #4A5568; border-radius: 0; font-size: 14px; font-family: 'Courier New', monospace; font-weight: bold; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); color: #2D3748; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8); }";
    html += ".stock-input-row input:focus { border-color: #D69E2E; outline: none; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8), 0 0 0 2px rgba(214, 158, 46, 0.3); }";
    html += ".stock-input-row input::placeholder { color: #A0AEC0; font-style: italic; }";
    html += ".menu-button { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 120px; margin: 8px 4px; padding: 12px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:active { transform: translate(2px, 2px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.3), inset -1px -1px 0px rgba(255,255,255,0.1); }";
    html += ".menu-button.primary { background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%); border-color: #744210; text-shadow: 1px 1px 0px #744210; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #744210; }";
    html += ".menu-button.primary:hover { background: linear-gradient(145deg, #B7791F 0%, #975A16 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #744210; }";
    html += ".menu-button:disabled { background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); border-color: #4A5568; cursor: not-allowed; text-shadow: 1px 1px 0px #4A5568; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #4A5568; transform: none; }";
    html += ".button-group { text-align: center; margin-top: 25px; position: relative; z-index: 1; }";
    html += ".status-message { display: none; margin: 20px 0; padding: 15px; border-radius: 0; text-align: center; position: relative; z-index: 1; font-family: 'Courier New', monospace; font-weight: bold; }";
    html += ".status-success { background: linear-gradient(145deg, #C6F6D5 0%, #9AE6B4 100%); color: #22543D; border: 3px solid #38A169; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.1), 2px 2px 0px #2F855A; }";
    html += ".status-error { background: linear-gradient(145deg, #FED7D7 0%, #FEB2B2 100%); color: #742A2A; border: 3px solid #E53E3E; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.1), 2px 2px 0px #C53030; }";
    html += "@media (min-width: 768px) { .container { max-width: 650px; } .header h1 { font-size: 28px; } .menu-button { font-size: 15px; padding: 14px 24px; } }";
    html += "@media (max-width: 360px) { body { padding: 15px; } .header { padding: 20px 15px; } .header h1 { font-size: 22px; } .content { padding: 20px 15px; } }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>股票设置</h1>";
    html += "</div>";
    html += "<div class='content'>";
    html += "<form id='stockForm' onsubmit='return handleStockSettings(event)'>";
    html += "<div class='form-group'>";
    html += "<label>股票功能</label>";
    html += "<label class='switch'>";
    html += "<input type='checkbox' id='stockEnabled' name='stockEnabled'";
    if (stockModeEnabled) {
        html += " checked";
    }
    html += ">";
    html += "<span class='slider'></span>";
    html += "</label>";
    html += "<div class='description'>开启后将显示股票信息，关闭后回到马里奥时钟模式</div>";
    html += "<div class='description warning'>注意：设置保存后将立即切换到新模式</div>";
    html += "</div>";
    html += "<div class='form-group'>";
    html += "<label>监控股票（最多4只）</label>";
    html += "<div class='stock-inputs'>";
    for (int i = 0; i < 4; i++) {
        html += "<div class='stock-input-row'>";
        html += "<label>股票" + String(i + 1) + ":</label>";
        html += "<input type='text' name='stock" + String(i) + "' maxlength='6' placeholder='6位股票代码' value='" + stockCodes[i] + "'>";
        html += "</div>";
    }
    html += "</div>";
    html += "</div>";
    html += "<div id='statusMessage' class='status-message'></div>";
    html += "<div class='button-group'>";
    html += "<button type='submit' class='menu-button primary'>确认</button>";
    html += "<a href='/' class='menu-button'>返回</a>";
    html += "</div>";
    html += "</form>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "function handleStockSettings(event) {";
    html += "  event.preventDefault();";
    html += "  const formData = new FormData(event.target);";
    html += "  const stockEnabled = document.getElementById('stockEnabled').checked;";
    html += "  formData.set('stockEnabled', stockEnabled ? 'true' : 'false');";
    html += "  fetch('/stocksettings', {";
    html += "    method: 'POST',";
    html += "    body: formData";
    html += "  })";
    html += "  .then(response => response.json())";
    html += "  .then(data => {";
    html += "    const statusDiv = document.getElementById('statusMessage');";
    html += "    statusDiv.style.display = 'block';";
    html += "    if (data.status === 'success') {";
    html += "      statusDiv.className = 'status-message status-success';";
    html += "      statusDiv.textContent = data.message;";
    html += "    } else {";
    html += "      statusDiv.className = 'status-message status-error';";
    html += "      statusDiv.textContent = data.message;";
    html += "    }";
    html += "  })";
    html += "  .catch(error => {";
    html += "    const statusDiv = document.getElementById('statusMessage');";
    html += "    statusDiv.style.display = 'block';";
    html += "    statusDiv.className = 'status-message status-error';";
    html += "    statusDiv.textContent = '设置失败，请重试';";
    html += "  });";
    html += "  return false;";
    html += "}";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Served stock config page");
}

void WifiManager::handleStockSettings() {
    // 获取表单数据
    String stockEnabledStr = server.arg("stockEnabled");

    // 验证输入
    if (stockEnabledStr.length() == 0) {
        server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"股票功能开关不能为空\"}");
        return;
    }

    bool stockEnabled = (stockEnabledStr == "true");

    // 获取股票代码
    String codes[4];
    for (int i = 0; i < 4; i++) {
        String argName = "stock" + String(i);
        codes[i] = server.arg(argName);
        codes[i].trim(); // 去除前后空格

        USBSerial.print("接收到股票代码");
        USBSerial.print(i + 1);
        USBSerial.print(": ");
        USBSerial.println(codes[i]);

        // 验证股票代码格式
        if (codes[i].length() > 0 && !isValidStockCode(codes[i])) {
            String errorMsg = "股票代码" + String(i + 1) + "格式错误，请输入6位数字";
            server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"" + errorMsg + "\"}");
            return;
        }
    }

    USBSerial.print("Setting stock mode: ");
    USBSerial.println(stockEnabled ? "enabled" : "disabled");

    // 保存设置到Flash
    saveStockSettings(stockEnabled, codes);

    // 返回成功响应
    String json = "{\"status\":\"success\",\"stockEnabled\":" + String(stockEnabled ? "true" : "false") + ",\"message\":\"股票设置保存成功\"}";
    server.send(200, "application/json", json);
    USBSerial.println("股票设置保存成功");

    // 触发模式切换回调，而不是重启设备
    if (modeChangeCallback != nullptr) {
        modeChangeCallback();
    }
}

void WifiManager::saveStockSettings(bool enabled, String codes[4]) {
    preferences.begin("stock", false);
    preferences.putBool("enabled", enabled);

    // 保存股票代码
    for (int i = 0; i < 4; i++) {
        String key = "code" + String(i);
        preferences.putString(key.c_str(), codes[i]);
    }

    preferences.end();

    // 更新内存中的设置
    stockModeEnabled = enabled;
    for (int i = 0; i < 4; i++) {
        stockCodes[i] = codes[i];
    }

    USBSerial.println("Stock settings saved to Flash");
    USBSerial.print("Stock Mode Enabled: ");
    USBSerial.println(enabled ? "true" : "false");
    for (int i = 0; i < 4; i++) {
        if (codes[i].length() > 0) {
            USBSerial.print("Stock Code ");
            USBSerial.print(i + 1);
            USBSerial.print(": ");
            USBSerial.println(codes[i]);
        }
    }
}

void WifiManager::loadStockSettings() {
    preferences.begin("stock", true);
    stockModeEnabled = preferences.getBool("enabled", false);  // 默认关闭股票功能

    // 加载股票代码
    for (int i = 0; i < 4; i++) {
        String key = "code" + String(i);
        stockCodes[i] = preferences.getString(key.c_str(), "");
    }

    preferences.end();

    USBSerial.println("Stock settings loaded from Flash");
    USBSerial.print("Stock Mode Enabled: ");
    USBSerial.println(stockModeEnabled ? "true" : "false");
    for (int i = 0; i < 4; i++) {
        if (stockCodes[i].length() > 0) {
            USBSerial.print("Stock Code ");
            USBSerial.print(i + 1);
            USBSerial.print(": ");
            USBSerial.println(stockCodes[i]);
        }
    }
}

void WifiManager::handleGallery() {
    // 创建电子相册页面
    String html = "<!DOCTYPE html>";
    html += "<html lang='zh-CN'>";
    html += "<head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>";
    html += "<meta name='format-detection' content='telephone=no'>";
    html += "<title>电子相册 - 马里奥时钟</title>";
    html += "<style>";
    html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
    html += "body { font-family: 'Courier New', monospace; background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%); min-height: 100vh; padding: 20px; -webkit-tap-highlight-color: transparent; position: relative; }";
    html += "body::before { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.8) 15px, transparent 15px), radial-gradient(circle at 80% 30%, rgba(255,255,255,0.6) 20px, transparent 20px), radial-gradient(circle at 40% 70%, rgba(255,255,255,0.7) 18px, transparent 18px), radial-gradient(circle at 70% 80%, rgba(255,255,255,0.5) 12px, transparent 12px); background-size: 200px 200px, 300px 300px, 250px 250px, 180px 180px; animation: cloudMove 20s linear infinite; pointer-events: none; z-index: 0; }";
    html += "@keyframes cloudMove { 0% { transform: translateX(-50px); } 100% { transform: translateX(50px); } }";
    html += ".container { max-width: 550px; margin: 0 auto; background: linear-gradient(145deg, #8B4513 0%, #A0522D 100%); border: 4px solid #654321; border-radius: 0; box-shadow: inset 2px 2px 0px #CD853F, inset -2px -2px 0px #5D4037, 4px 4px 0px #3E2723; overflow: hidden; position: relative; z-index: 1; animation: blockAppear 0.6s ease-out; }";
    html += "@keyframes blockAppear { 0% { opacity: 0; transform: scale(0.8) translateY(20px); } 100% { opacity: 1; transform: scale(1) translateY(0); } }";
    html += ".container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(90deg, #654321 1px, transparent 1px), linear-gradient(180deg, #654321 1px, transparent 1px); background-size: 20px 20px; opacity: 0.3; pointer-events: none; }";
    html += ".header { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); color: white; text-align: center; padding: 25px 20px; position: relative; border-bottom: 4px solid #B91C1C; }";
    html += ".header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%); background-size: 8px 8px; background-position: 0 0, 4px 4px; }";
    html += ".header h1 { font-size: 24px; font-weight: bold; position: relative; z-index: 1; text-shadow: 2px 2px 0px #8B0000; letter-spacing: 1px; }";
    html += ".content { background: linear-gradient(145deg, #F7FAFC 0%, #E2E8F0 100%); padding: 25px 20px; position: relative; }";
    html += ".form-group { margin-bottom: 25px; text-align: left; position: relative; z-index: 1; }";
    html += ".form-group > label:first-child { display: block; margin-bottom: 12px; font-weight: bold; color: #2D3748; font-size: 16px; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += ".switch { position: relative; display: inline-block; width: 70px; height: 40px; margin-top: 8px; }";
    html += ".switch input { opacity: 0; width: 0; height: 0; }";
    html += ".slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); transition: .3s; border: 3px solid #4A5568; border-radius: 0; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #2D3748; }";
    html += ".slider:before { position: absolute; content: ''; height: 28px; width: 28px; left: 4px; bottom: 4px; background: linear-gradient(145deg, #FFFFFF 0%, #F7FAFC 100%); transition: .3s; border: 2px solid #4A5568; border-radius: 0; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.2); }";
    html += "input:checked + .slider { background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); border-color: #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += "input:checked + .slider:before { transform: translateX(26px); background: linear-gradient(145deg, #FFF5B7 0%, #FED7AA 100%); border-color: #D69E2E; }";
    html += ".description { color: #4A5568; font-size: 12px; margin-top: 8px; line-height: 1.4; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); font-family: 'Courier New', monospace; }";
    html += ".description.warning { color: #D69E2E; font-weight: bold; background: rgba(214, 158, 46, 0.1); padding: 8px; border: 2px solid #D69E2E; margin-top: 12px; border-radius: 0; }";
    html += ".upload-area { margin: 15px 0; position: relative; z-index: 1; display: flex; align-items: center; gap: 10px; flex-wrap: wrap; }";
    html += ".file-input-wrapper { position: relative; display: inline-block; overflow: hidden; }";
    html += ".file-input-wrapper input[type=file] { position: absolute; left: -9999px; }";
    html += ".file-input-label { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 120px; padding: 12px 16px; background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); color: white; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".file-input-label:hover { background: linear-gradient(145deg, #2F855A 0%, #276749 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button { display: inline-flex; align-items: center; justify-content: center; gap: 8px; min-width: 120px; margin: 8px 4px; padding: 12px 20px; background: linear-gradient(145deg, #3182CE 0%, #2B6CB0 100%); color: white; text-decoration: none; border: 3px solid #1A365D; border-radius: 0; font-size: 14px; font-weight: bold; font-family: 'Courier New', monospace; text-align: center; cursor: pointer; transition: all 0.1s ease; position: relative; text-shadow: 1px 1px 0px #1A365D; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #1A365D; }";
    html += ".menu-button:hover { background: linear-gradient(145deg, #2B6CB0 0%, #2C5282 100%); transform: translate(1px, 1px); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #1A365D; }";
    html += ".menu-button:active { transform: translate(2px, 2px); box-shadow: inset 1px 1px 0px rgba(0,0,0,0.3), inset -1px -1px 0px rgba(255,255,255,0.1); }";
    html += ".menu-button.primary { background: linear-gradient(145deg, #D69E2E 0%, #B7791F 100%); border-color: #744210; text-shadow: 1px 1px 0px #744210; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #744210; }";
    html += ".menu-button.primary:hover { background: linear-gradient(145deg, #B7791F 0%, #975A16 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.4), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #744210; }";
    html += ".menu-button.danger { background: linear-gradient(145deg, #E53E3E 0%, #C53030 100%); border-color: #742A2A; text-shadow: 1px 1px 0px #742A2A; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #742A2A; }";
    html += ".menu-button.danger:hover { background: linear-gradient(145deg, #C53030 0%, #9C1C1C 100%); box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.3), 1px 1px 0px #742A2A; }";
    html += ".menu-button:disabled { background: linear-gradient(145deg, #A0AEC0 0%, #718096 100%); border-color: #4A5568; cursor: not-allowed; text-shadow: 1px 1px 0px #4A5568; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.2), inset -1px -1px 0px rgba(0,0,0,0.3), 2px 2px 0px #4A5568; transform: none; }";
    html += ".button-group { text-align: center; margin-top: 25px; position: relative; z-index: 1; }";
    html += ".status-message { display: none; margin: 20px 0; padding: 15px; border-radius: 0; text-align: center; position: relative; z-index: 1; font-family: 'Courier New', monospace; font-weight: bold; }";
    html += ".status-success { background: linear-gradient(145deg, #C6F6D5 0%, #9AE6B4 100%); color: #22543D; border: 3px solid #38A169; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.1), 2px 2px 0px #2F855A; }";
    html += ".status-error { background: linear-gradient(145deg, #FED7D7 0%, #FEB2B2 100%); color: #742A2A; border: 3px solid #E53E3E; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.1), 2px 2px 0px #C53030; }";
    html += ".file-info { margin: 15px 0; padding: 15px; background: linear-gradient(145deg, #EDF2F7 0%, #E2E8F0 100%); border: 3px solid #CBD5E0; border-radius: 0; position: relative; z-index: 1; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.8), inset -1px -1px 0px rgba(0,0,0,0.1), 2px 2px 0px #A0AEC0; }";
    html += ".file-info p { margin: 6px 0; font-size: 12px; color: #4A5568; font-family: 'Courier New', monospace; font-weight: bold; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += ".upload-progress { margin: 20px 0; position: relative; z-index: 1; }";
    html += ".progress-bar { width: 100%; height: 25px; background: linear-gradient(145deg, #E2E8F0 0%, #CBD5E0 100%); border: 3px solid #A0AEC0; border-radius: 0; overflow: hidden; box-shadow: inset 1px 1px 0px rgba(0,0,0,0.2), inset -1px -1px 0px rgba(255,255,255,0.8); }";
    html += ".progress-fill { height: 100%; background: linear-gradient(145deg, #38A169 0%, #2F855A 100%); width: 0%; transition: width 0.3s ease; box-shadow: inset 1px 1px 0px rgba(255,255,255,0.3), inset -1px -1px 0px rgba(0,0,0,0.2); }";
    html += "#progressText { margin: 10px 0 0 0; font-size: 12px; color: #4A5568; text-align: center; font-family: 'Courier New', monospace; font-weight: bold; text-shadow: 1px 1px 0px rgba(255,255,255,0.8); }";
    html += "form { max-width: 450px; margin: 0 auto; position: relative; z-index: 1; }";
    html += "@media (min-width: 768px) { .container { max-width: 650px; } .header h1 { font-size: 28px; } .menu-button { font-size: 15px; padding: 14px 24px; } }";
    html += "@media (max-width: 360px) { body { padding: 15px; } .header { padding: 20px 15px; } .header h1 { font-size: 22px; } .content { padding: 20px 15px; } }";
    html += "</style>";
    html += "</head>";
    html += "<body>";
    html += "<div class='container'>";
    html += "<div class='header'>";
    html += "<h1>电子相册</h1>";
    html += "</div>";
    html += "<div class='content'>";
    html += "<form id='galleryForm' action='/gallerysettings' method='POST' onsubmit='return handleGallerySettings(event)'>";
    html += "<div class='form-group'>";
    html += "<label>相册功能</label>";
    html += "<label class='switch'>";
    html += "<input type='checkbox' id='galleryEnabled' name='galleryEnabled'";
    if (galleryModeEnabled) {
        html += " checked";
    }
    html += ">";
    html += "<span class='slider'></span>";
    html += "</label>";
    html += "<div class='description'>开启后将显示照片壁纸，关闭后回到马里奥时钟模式</div>";
    html += "<div class='description warning'>注意：设置保存后将立即切换到新模式</div>";
    html += "</div>";
    html += "<div class='form-group'>";
    html += "<label>照片管理:</label>";

    // 检查是否有照片存在（用于控制删除按钮显示，不显示状态文字）
    bool hasPhoto = LittleFS.exists("/gallery/current_photo.jpg") && LittleFS.exists("/gallery/photo_info.dat");

    html += "<div class='upload-area'>";
    html += "<div class='file-input-wrapper'>";
    html += "<input type='file' id='photoFile' accept='.jpg,.jpeg,.bmp' onchange='validateFile(this)'>";
    html += "<label for='photoFile' class='file-input-label'>选择照片</label>";
    html += "</div>";
    html += "<button type='button' class='menu-button danger' id='deleteBtn' onclick='deletePhoto()' style='display:" + String(hasPhoto ? "inline-flex" : "none") + "; align-items: center; justify-content: center; min-width: 120px; padding: 12px 16px; font-size: 14px;'>删除照片</button>";
    html += "</div>";
    html += "<div class='file-info' id='fileInfo' style='display:none;'>";
    html += "<p id='fileName'></p>";
    html += "<p id='fileSize'></p>";
    html += "</div>";
    html += "<div class='upload-progress' id='uploadProgress' style='display:none;'>";
    html += "<div class='progress-bar'>";
    html += "<div class='progress-fill' id='progressFill'></div>";
    html += "</div>";
    html += "<p id='progressText'>准备上传...</p>";
    html += "</div>";
    html += "</div>";
    html += "<div class='form-group'>";
    html += "<label>功能说明:</label>";
    html += "<p>• <strong>推荐格式：JPEG</strong>（最佳兼容性和压缩效果）</p>";
    html += "<p>• 支持格式：JPEG (.jpg, .jpeg)、BMP (.bmp)</p>";
    html += "<p>• 文件大小限制：100KB</p>";
    html += "<p>• 自动缩放适应240x240屏幕，保持原始比例</p>";
    html += "<p>• 上传成功后照片将立即显示在屏幕上</p>";
    html += "<p style='color: #ff6600;'>注意：暂不支持PNG格式，请转换为JPEG格式</p>";
    html += "</div>";
    html += "<div id='statusMessage' class='status-message'></div>";
    html += "<div class='button-group'>";
    html += "<button type='submit' class='menu-button primary'>确认</button>";
    html += "<a href='/' class='menu-button'>返回</a>";
    html += "</div>";
    html += "</form>";
    html += "</div>";
    html += "</div>";
    html += "<script>";
    html += "function handleGallerySettings(event) {";
    html += "  event.preventDefault();";
    html += "  const formData = new FormData(event.target);";
    html += "  const galleryEnabled = document.getElementById('galleryEnabled').checked;";
    html += "  formData.set('galleryEnabled', galleryEnabled ? 'true' : 'false');";
    html += "  fetch('/gallerysettings', {";
    html += "    method: 'POST',";
    html += "    body: formData";
    html += "  })";
    html += "  .then(response => response.json())";
    html += "  .then(data => {";
    html += "    const statusDiv = document.getElementById('statusMessage');";
    html += "    if (data.status === 'success') {";
    html += "      statusDiv.className = 'status-message status-success';";
    html += "      statusDiv.textContent = '设置保存成功！';";
    html += "      if (galleryEnabled) {";
    html += "        statusDiv.textContent += ' 正在切换模式...';";
    html += "      }";
    html += "    } else {";
    html += "      statusDiv.className = 'status-message status-error';";
    html += "      statusDiv.textContent = '设置保存失败：' + data.message;";
    html += "    }";
    html += "    statusDiv.style.display = 'block';";
    html += "  })";
    html += "  .catch(error => {";
    html += "    const statusDiv = document.getElementById('statusMessage');";
    html += "    statusDiv.className = 'status-message status-error';";
    html += "    statusDiv.textContent = '设置失败，请重试';";
    html += "    statusDiv.style.display = 'block';";
    html += "  });";
    html += "  return false;";
    html += "}";
    html += "function validateFile(input) {";
    html += "  const file = input.files[0];";
    html += "  if (!file) return;";
    html += "  const maxSize = 100 * 1024;";
    html += "  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/bmp'];";
    html += "  const allowedExts = ['.jpg', '.jpeg', '.bmp'];";
    html += "  const fileName = file.name.toLowerCase();";
    html += "  const fileExt = fileName.substring(fileName.lastIndexOf('.'));";
    html += "  if (file.size > maxSize) {";
    html += "    showStatusMessage('error', '文件大小超过100KB限制，请选择更小的文件');";
    html += "    input.value = '';";
    html += "    return;";
    html += "  }";
    html += "  if (!allowedExts.includes(fileExt)) {";
    html += "    showStatusMessage('error', '仅支持JPEG、BMP格式的图片文件');";
    html += "    input.value = '';";
    html += "    return;";
    html += "  }";
    html += "  document.getElementById('fileName').textContent = '文件名: ' + file.name;";
    html += "  document.getElementById('fileSize').textContent = '大小: ' + (file.size / 1024).toFixed(1) + ' KB';";
    html += "  document.getElementById('fileInfo').style.display = 'block';";
    html += "  uploadPhoto(file);";
    html += "}";
    html += "function uploadPhoto(file) {";
    html += "  const formData = new FormData();";
    html += "  formData.append('photo', file);";
    html += "  document.getElementById('uploadProgress').style.display = 'block';";
    html += "  updateProgress(0, '开始上传...');";
    html += "  const xhr = new XMLHttpRequest();";
    html += "  xhr.upload.addEventListener('progress', function(e) {";
    html += "    if (e.lengthComputable) {";
    html += "      const percentComplete = (e.loaded / e.total) * 100;";
    html += "      updateProgress(percentComplete, '上传中... ' + percentComplete.toFixed(1) + '%');";
    html += "    }";
    html += "  });";
    html += "  xhr.addEventListener('load', function() {";
    html += "    if (xhr.status === 200) {";
    html += "      try {";
    html += "        const data = JSON.parse(xhr.responseText);";
    html += "        if (data.status === 'success') {";
    html += "          updateProgress(100, '处理完成');";
    html += "          showStatusMessage('success', '照片上传成功！新照片已显示在屏幕上');";
    html += "          setTimeout(() => { location.reload(); }, 2000);";
    html += "        } else {";
    html += "          updateProgress(0, '处理失败');";
    html += "          showStatusMessage('error', '处理失败：' + data.message);";
    html += "        }";
    html += "      } catch(e) {";
    html += "        updateProgress(0, '响应解析失败');";
    html += "        showStatusMessage('error', '服务器响应格式错误');";
    html += "      }";
    html += "    } else {";
    html += "      updateProgress(0, '上传失败');";
    html += "      showStatusMessage('error', '上传失败，HTTP状态: ' + xhr.status);";
    html += "    }";
    html += "  });";
    html += "  xhr.addEventListener('error', function() {";
    html += "    updateProgress(0, '网络错误');";
    html += "    showStatusMessage('error', '网络连接失败');";
    html += "  });";
    html += "  xhr.open('POST', '/photoupload');";
    html += "  xhr.send(formData);";
    html += "}";
    html += "function updateProgress(percent, text) {";
    html += "  document.getElementById('progressFill').style.width = percent + '%';";
    html += "  document.getElementById('progressText').textContent = text;";
    html += "}";
    html += "function showStatusMessage(type, message) {";
    html += "  const statusDiv = document.getElementById('statusMessage');";
    html += "  statusDiv.className = 'status-message status-' + type;";
    html += "  statusDiv.textContent = message;";
    html += "  statusDiv.style.display = 'block';";
    html += "}";
    html += "function deletePhoto() {";
    html += "  if (confirm('确定要删除当前照片吗？')) {";
    html += "    fetch('/photodelete', { method: 'POST' })";
    html += "    .then(response => response.json())";
    html += "    .then(data => {";
    html += "      if (data.status === 'success') {";
    html += "        showStatusMessage('success', '照片删除成功！');";
    html += "        document.getElementById('deleteBtn').style.display = 'none';";
    html += "      } else {";
    html += "        showStatusMessage('error', '删除失败：' + data.message);";
    html += "      }";
    html += "    })";
    html += "    .catch(error => {";
    html += "      showStatusMessage('error', '删除失败，请重试');";
    html += "    });";
    html += "  }";
    html += "}";
    html += "let lastTouchEnd = 0;";
    html += "document.addEventListener('touchend', function (event) {";
    html += "  const now = (new Date()).getTime();";
    html += "  if (now - lastTouchEnd <= 300) {";
    html += "    event.preventDefault();";
    html += "  }";
    html += "  lastTouchEnd = now;";
    html += "}, false);";
    html += "</script>";
    html += "</body>";
    html += "</html>";

    server.send(200, "text/html", html);
    USBSerial.println("Served gallery page");
}

void WifiManager::saveGallerySettings(bool enabled) {
    preferences.begin("gallery", false);
    preferences.putBool("enabled", enabled);
    preferences.end();

    // 更新内存中的设置
    galleryModeEnabled = enabled;

    USBSerial.println("Gallery settings saved to Flash");
    USBSerial.print("Gallery Mode Enabled: ");
    USBSerial.println(enabled ? "true" : "false");
}

void WifiManager::loadGallerySettings() {
    preferences.begin("gallery", true);
    galleryModeEnabled = preferences.getBool("enabled", false);  // 默认关闭相册功能
    preferences.end();

    USBSerial.println("Gallery settings loaded from Flash");
    USBSerial.print("Gallery Mode Enabled: ");
    USBSerial.println(galleryModeEnabled ? "true" : "false");
}

void WifiManager::handlePhotoUpload() {
    HTTPUpload& upload = server.upload();
    static File uploadFile;
    static String tempFileName = "/gallery/temp_upload.tmp";
    static String uploadedFileName = "";
    static bool uploadSuccess = false;

    if (upload.status == UPLOAD_FILE_START) {
        USBSerial.printf("开始上传文件: %s, 大小: %d\n", upload.filename.c_str(), upload.totalSize);
        uploadedFileName = upload.filename;
        uploadSuccess = false;

        // 清理可能存在的临时文件
        if (LittleFS.exists(tempFileName)) {
            USBSerial.println("清理旧的临时文件");
            LittleFS.remove(tempFileName);
        }

        // 检查文件大小
        if (upload.totalSize > 100 * 1024) {
            USBSerial.println("错误：文件过大");
            return;
        }

        // 检查存储空间
        size_t freeBytes = LittleFS.totalBytes() - LittleFS.usedBytes();
        if (freeBytes < upload.totalSize + 20000) { // 预留20KB空间
            USBSerial.println("错误：存储空间不足");
            return;
        }

        // 创建临时文件
        USBSerial.printf("尝试创建临时文件: %s\n", tempFileName.c_str());
        uploadFile = LittleFS.open(tempFileName, "w");
        if (!uploadFile) {
            USBSerial.println("错误：无法创建临时文件");
            return;
        }
        USBSerial.println("临时文件创建成功");

    } else if (upload.status == UPLOAD_FILE_WRITE) {
        // 写入数据块
        if (uploadFile) {
            size_t written = uploadFile.write(upload.buf, upload.currentSize);
            USBSerial.printf("写入数据块: %d/%d字节\n", written, upload.currentSize);
        } else {
            USBSerial.println("警告：临时文件句柄无效");
        }

    } else if (upload.status == UPLOAD_FILE_END) {
        if (uploadFile) {
            USBSerial.println("关闭临时文件");
            uploadFile.close();
        } else {
            USBSerial.println("警告：临时文件句柄为空");
        }

        USBSerial.printf("文件上传完成，大小: %d字节\n", upload.totalSize);

        // 验证临时文件是否存在
        USBSerial.printf("检查临时文件是否存在: %s\n", tempFileName.c_str());
        if (!LittleFS.exists(tempFileName)) {
            USBSerial.println("错误：临时文件不存在");

            // 列出gallery目录内容进行调试
            File dir = LittleFS.open("/gallery");
            if (dir && dir.isDirectory()) {
                USBSerial.println("Gallery目录内容:");
                File file = dir.openNextFile();
                while (file) {
                    USBSerial.printf("  - %s (%d字节)\n", file.name(), file.size());
                    file = dir.openNextFile();
                }
                dir.close();
            } else {
                USBSerial.println("无法打开gallery目录");
            }

            server.send(500, "application/json", "{\"status\":\"error\",\"message\":\"临时文件丢失\"}");
            return;
        }
        USBSerial.println("临时文件存在，开始处理");

        // 调用图片处理函数（显示+保存）
        if (photoGallery && photoGallery->processImageFile(tempFileName, uploadedFileName)) {
            USBSerial.println("照片处理、显示和保存成功");
            uploadSuccess = true;

            // 清理临时文件
            LittleFS.remove(tempFileName);

            // 不需要重启，照片已经显示在屏幕上
            USBSerial.println("照片上传完成，已显示在屏幕上");
        } else {
            // 处理失败，清理临时文件
            LittleFS.remove(tempFileName);
            USBSerial.println("照片处理失败");
            uploadSuccess = false;
        }

    } else if (upload.status == UPLOAD_FILE_ABORTED) {
        USBSerial.println("文件上传被中断");
        if (uploadFile) {
            uploadFile.close();
        }
        // 清理临时文件
        LittleFS.remove(tempFileName);
        uploadSuccess = false;
    }
}

void WifiManager::handlePhotoDelete() {
    USBSerial.println("处理照片删除请求");

    // 检查是否有照片存在
    if (!LittleFS.exists("/gallery/current_photo.jpg") && !LittleFS.exists("/gallery/photo_info.dat")) {
        server.send(404, "application/json", "{\"status\":\"error\",\"message\":\"没有照片可删除\"}");
        return;
    }

    // 删除照片文件
    bool success = true;
    if (LittleFS.exists("/gallery/current_photo.jpg")) {
        success &= LittleFS.remove("/gallery/current_photo.jpg");
    }
    if (LittleFS.exists("/gallery/photo_info.dat")) {
        success &= LittleFS.remove("/gallery/photo_info.dat");
    }

    if (success) {
        server.send(200, "application/json", "{\"status\":\"success\",\"message\":\"照片删除成功\"}");
        USBSerial.println("照片删除成功");
    } else {
        server.send(500, "application/json", "{\"status\":\"error\",\"message\":\"删除失败\"}");
        USBSerial.println("照片删除失败");
    }
}

void WifiManager::handleGallerySettings() {
    // 获取表单数据
    String galleryEnabledStr = server.arg("galleryEnabled");

    // 验证输入
    if (galleryEnabledStr.length() == 0) {
        server.send(400, "application/json", "{\"status\":\"error\",\"message\":\"相册功能开关不能为空\"}");
        return;
    }

    bool enabled = (galleryEnabledStr == "true");

    USBSerial.print("Setting gallery mode: ");
    USBSerial.println(enabled ? "enabled" : "disabled");

    // 保存设置到Flash
    saveGallerySettings(enabled);

    // 返回成功响应
    String json = "{\"status\":\"success\",\"galleryEnabled\":" + String(enabled ? "true" : "false") + ",\"message\":\"";
    if (enabled) {
        json += "相册模式已启用\"}";
    } else {
        json += "相册模式已关闭\"}";
    }

    server.send(200, "application/json", json);
    USBSerial.println("Gallery settings saved successfully");

    // 触发模式切换回调，而不是重启设备
    USBSerial.println("相册设置已更改，触发模式切换");
    if (modeChangeCallback != nullptr) {
        modeChangeCallback();
    }
}

bool WifiManager::isValidStockCode(String code) {
    // 检查是否为6位数字
    if (code.length() != 6) return false;

    for (int i = 0; i < 6; i++) {
        if (!isDigit(code.charAt(i))) return false;
    }

    // 检查是否为有效的交易所代码
    return code.startsWith("6") || code.startsWith("0") || code.startsWith("3");
}

bool WifiManager::isAPModeActive() const {
    // 检查WiFi AP是否开启
    return WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA;
}
